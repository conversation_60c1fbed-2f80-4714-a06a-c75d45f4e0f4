#!/usr/bin/env python3
"""
Generate test data for the security monitor dashboards
This script creates sample security events and alerts for testing
"""

import sys
import os
from datetime import datetime, timedelta
import random

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def generate_test_events():
    """Generate test security events"""
    
    # Sample data
    sample_ips = [
        '*************', '*********', '***********', 
        '************', '*************', '**********',
        '127.0.0.1', '*************'
    ]
    
    sample_usernames = [
        'admin', 'user1', 'hacker', 'test', 'guest', 
        'root', 'administrator', 'demo'
    ]
    
    event_types = [
        'failed_login', 'successful_login', 'rate_limit_exceeded',
        'user_registration', 'challenge_submission', 'admin_action'
    ]
    
    events = []
    
    # Generate events for the last 24 hours
    now = datetime.utcnow()
    
    for i in range(100):  # Generate 100 test events
        # Random time in the last 24 hours
        hours_ago = random.uniform(0, 24)
        timestamp = now - timedelta(hours=hours_ago)
        
        event = {
            'timestamp': timestamp.isoformat(),
            'event_type': random.choice(event_types),
            'ip': random.choice(sample_ips),
            'details': {
                'username': random.choice(sample_usernames),
                'user_agent': 'Mozilla/5.0 (Test Browser)',
                'endpoint': random.choice(['/login', '/register', '/challenges', '/admin'])
            }
        }
        
        events.append(event)
    
    return events

def generate_test_alerts():
    """Generate test security alerts"""
    
    alerts = [
        {
            'timestamp': datetime.utcnow().isoformat(),
            'severity': 'critical',
            'title': 'Multiple Failed Login Attempts',
            'message': 'IP ************ has 15 failed login attempts in 5 minutes',
            'ip_address': '************'
        },
        {
            'timestamp': (datetime.utcnow() - timedelta(hours=2)).isoformat(),
            'severity': 'warning',
            'title': 'Rate Limit Exceeded',
            'message': 'IP ************* exceeded rate limits on /challenges endpoint',
            'ip_address': '*************'
        },
        {
            'timestamp': (datetime.utcnow() - timedelta(hours=6)).isoformat(),
            'severity': 'info',
            'title': 'New User Registration Spike',
            'message': '10 new users registered in the last hour',
            'ip_address': None
        }
    ]
    
    return alerts

def save_test_data():
    """Save test data to JSON files"""
    import json
    
    # Create test data directory
    test_dir = "test_data"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # Generate and save events
    events = generate_test_events()
    with open(f"{test_dir}/security_events.json", 'w') as f:
        json.dump(events, f, indent=2)
    
    # Generate and save alerts
    alerts = generate_test_alerts()
    with open(f"{test_dir}/security_alerts.json", 'w') as f:
        json.dump(alerts, f, indent=2)
    
    print(f"✅ Generated {len(events)} test security events")
    print(f"✅ Generated {len(alerts)} test security alerts")
    print(f"📁 Test data saved to {test_dir}/ directory")
    
    return events, alerts

def create_sample_platform_data():
    """Create sample platform statistics"""
    
    platform_stats = {
        'total_users': random.randint(50, 500),
        'total_teams': random.randint(10, 100),
        'total_challenges': random.randint(20, 50),
        'total_submissions': random.randint(200, 2000),
        'recent_users': random.randint(0, 20),
        'recent_submissions': random.randint(5, 100),
        'success_rate': round(random.uniform(15, 85), 1)
    }
    
    # Sample top solvers
    top_solvers = [
        ('alice_hacker', random.randint(15, 30)),
        ('bob_security', random.randint(12, 25)),
        ('charlie_ctf', random.randint(10, 20)),
        ('diana_crypto', random.randint(8, 18)),
        ('eve_pwn', random.randint(5, 15))
    ]
    
    # Sample challenge stats
    challenge_stats = [
        ('Web Challenge 1', 'Web', random.randint(20, 100), random.randint(5, 30)),
        ('Crypto Puzzle', 'Crypto', random.randint(15, 80), random.randint(3, 20)),
        ('Binary Exploit', 'Pwn', random.randint(10, 50), random.randint(1, 10)),
        ('Forensics Mystery', 'Forensics', random.randint(25, 90), random.randint(8, 25)),
        ('Reverse Engineering', 'Reverse', random.randint(12, 60), random.randint(2, 15))
    ]
    
    return {
        'platform_stats': platform_stats,
        'top_solvers': top_solvers,
        'challenge_stats': challenge_stats
    }

def main():
    print("🎲 Generating Test Data for Security Monitor Dashboards...")
    print("=" * 60)
    
    # Generate security data
    events, alerts = save_test_data()
    
    # Generate platform data
    platform_data = create_sample_platform_data()
    
    print("\n📊 Sample Platform Statistics:")
    print("-" * 30)
    stats = platform_data['platform_stats']
    for key, value in stats.items():
        print(f"   {key.replace('_', ' ').title()}: {value}")
    
    print(f"\n🏆 Sample Top Solvers:")
    print("-" * 20)
    for name, solves in platform_data['top_solvers']:
        print(f"   {name}: {solves} solves")
    
    print(f"\n🎯 Sample Challenge Stats:")
    print("-" * 25)
    for name, category, attempts, solves in platform_data['challenge_stats']:
        rate = round((solves/attempts*100), 1) if attempts > 0 else 0
        print(f"   {name} ({category}): {solves}/{attempts} ({rate}%)")
    
    print(f"\n🔒 Sample Security Events:")
    print("-" * 25)
    event_counts = {}
    for event in events:
        event_type = event['event_type']
        event_counts[event_type] = event_counts.get(event_type, 0) + 1
    
    for event_type, count in event_counts.items():
        print(f"   {event_type.replace('_', ' ').title()}: {count}")
    
    print(f"\n🚨 Sample Security Alerts:")
    print("-" * 25)
    for alert in alerts:
        print(f"   [{alert['severity'].upper()}] {alert['title']}")
    
    print(f"\n✅ Test data generation complete!")
    print(f"   This data simulates what the dashboards will show with real activity.")

if __name__ == "__main__":
    main()
