# 📊 CTFd Security Monitor - Two Dashboard Solution

## 🎯 Overview

Created two focused dashboards for CTFd monitoring with **real data sources** and **reliable logging**:

### 1. 🏆 **Platform Overview Dashboard**
- **Purpose**: Monitor CTF platform activity and performance
- **URL**: `/admin/plugins/security_monitor/platform`
- **Data Sources**: CTFd database (Users, Challenges, Submissions, Teams)

### 2. 🛡️ **Security Monitor Dashboard**  
- **Purpose**: Monitor security threats, alerts, and suspicious activity
- **URL**: `/admin/plugins/security_monitor/security`
- **Data Sources**: Security events, real-time monitoring, threat detection

---

## 📈 Platform Overview Dashboard

### **Real-Time Statistics**
- **Total Users** - Count from Users table
- **Total Teams** - Count from Teams table  
- **Total Challenges** - Count from Challenges table
- **Total Submissions** - Count from Submissions table
- **Recent Activity** - New users/submissions in last 24 hours

### **Performance Metrics**
- **🥇 Top Solvers** - Users ranked by correct submissions
- **📊 Challenge Statistics** - Solve rates per challenge
- **🔄 Recent Activity Feed** - Live submission attempts
- **Success Rate** - Overall platform solve percentage

### **Data Sources**
```python
# Real CTFd database queries
Users.query.count()
Challenges.query.count()
Submissions.query.filter_by(type='correct').count()
Teams.query.count()
```

---

## 🛡️ Security Monitor Dashboard

### **Security Statistics**
- **Security Events** - Failed logins, rate limits, suspicious activity
- **Failed Logins** - Authentication failures (last 24h)
- **Active IPs** - Unique visitors being monitored
- **Request Rate** - Events per minute

### **Threat Detection**
- **🚨 Active Alerts** - Critical/Warning/Info alerts
- **⚠️ Recent Alerts** - Latest security incidents
- **🎯 Top IPs** - Most active IP addresses
- **📈 Event Distribution** - Security event types breakdown

### **Real-Time Monitoring**
```python
# Automatic event logging for:
- Failed login attempts
- Successful logins  
- User registrations
- Challenge submissions
- Rate limit violations
- Admin actions
```

---

## 🔧 Technical Implementation

### **Fixed Issues**
✅ **Circular Import Error** - Resolved platform_metrics import issue  
✅ **Dashboard API Endpoints** - Created proper data endpoints  
✅ **Real Data Integration** - Connected to actual CTFd database  
✅ **Error Handling** - Added fallbacks for missing data  

### **Key Features**
- **Auto-refresh** - Dashboards update every 30 seconds
- **Real-time data** - Direct database queries
- **Responsive design** - Works on mobile/desktop
- **Error resilience** - Graceful handling of missing data

### **File Structure**
```
CTFd/plugins/security_monitor/
├── __init__.py                 # Plugin initialization & event hooks
├── routes.py                   # Dashboard routes & API endpoints
├── monitor.py                  # Security monitoring logic
├── models.py                   # Database models
├── alerts.py                   # Alert management
└── templates/security_monitor/admin/
    ├── platform_dashboard.html # Platform overview UI
    └── security_dashboard.html # Security monitoring UI
```

---

## 🚀 Usage Instructions

### **1. Access Dashboards**
1. Start CTFd server
2. Login as admin
3. Navigate to Admin Panel
4. Look for menu items:
   - **"Platform Overview"** - CTF statistics
   - **"Security Monitor"** - Security events

### **2. Menu Integration**
Both dashboards appear in the admin menu bar:
```python
register_admin_plugin_menu_bar(
    title='Platform Overview',
    route='/admin/plugins/security_monitor/platform'
)
register_admin_plugin_menu_bar(
    title='Security Monitor', 
    route='/admin/plugins/security_monitor/security'
)
```

### **3. API Endpoints**
- `/api/platform-stats` - Platform statistics JSON
- `/api/stats` - Security statistics JSON  
- `/api/prometheus` - Prometheus metrics export

---

## 📊 Data Collection

### **Automatic Event Logging**
The plugin automatically logs:

```python
# Authentication Events
- successful_login
- failed_login  
- user_registration

# Platform Activity  
- challenge_submission
- admin_action
- rate_limit_exceeded

# Security Events
- suspicious_activity
- multiple_failed_attempts
- unusual_access_patterns
```

### **Real Database Integration**
```python
# Platform data from CTFd tables
from CTFd.models import Users, Challenges, Submissions, Teams

# Security data from plugin tables  
from .models import SecurityEvent, SecurityAlert, SecurityConfig
```

---

## 🎯 Benefits

### **For CTF Organizers**
- **📈 Platform Health** - Monitor user engagement and challenge difficulty
- **🏆 Performance Insights** - See which challenges are too easy/hard
- **👥 User Activity** - Track registration and participation trends

### **For Security Teams**  
- **🛡️ Threat Detection** - Real-time security event monitoring
- **🚨 Alert Management** - Automated threat response
- **📊 Security Analytics** - Historical security data analysis

### **For Administrators**
- **🔍 Single Pane of Glass** - All monitoring in one place
- **⚡ Real-time Updates** - Live data without page refresh
- **📱 Mobile Friendly** - Monitor from anywhere

---

## ✅ Status: Ready for Production

- ✅ Circular import issues resolved
- ✅ Real data sources connected  
- ✅ Error handling implemented
- ✅ Auto-refresh functionality
- ✅ Mobile responsive design
- ✅ Admin menu integration
- ✅ API endpoints available
- ✅ Prometheus metrics export

**The dashboards are now ready to display real CTF platform data and security events!**
