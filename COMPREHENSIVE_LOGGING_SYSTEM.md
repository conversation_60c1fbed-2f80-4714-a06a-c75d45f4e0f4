# 📋 Comprehensive Real-time Logging Dashboard System

## 🎯 Overview

Created a **comprehensive logging dashboard** that integrates and visualizes **real log data** from multiple sources within the CTFd platform, featuring:

- **Real-time log streaming** with WebSocket support
- **Multiple log source integration** (files, Docker, system logs)
- **Advanced filtering and search** capabilities
- **Live dashboard visualization** with auto-refresh
- **Log parsing for different formats** (CTFd, Nginx, system logs)

---

## 🔍 **Real Log Sources Integration**

### **Automatic Log Discovery**
```python
# Scans for available log sources
- CTFd application logs (./logs/*.log)
- System logs (/var/log/auth.log, /var/log/syslog)
- Web server logs (nginx, apache access/error logs)
- Docker container logs (via docker logs command)
- Database logs (MySQL, PostgreSQL)
```

### **Supported Log Formats**
- **CTFd Application Logs**: `2024-01-15 10:30:45,123 INFO [app.py:45] User login attempt`
- **Nginx Access Logs**: `************* - - [15/Jan/2024:10:30:45 +0000] "GET /login HTTP/1.1" 200 1234`
- **System Logs**: Standard syslog format
- **Docker Logs**: JSON and plain text formats
- **Custom Formats**: Extensible parser system

---

## 🔄 **Real-time Log Streaming**

### **Live Log Tailing**
```python
# Implements tail -f functionality
- Real-time file monitoring with inode tracking
- Log rotation detection and handling
- Efficient parsing without performance impact
- WebSocket streaming to browser (with polling fallback)
```

### **Multi-source Aggregation**
- **Simultaneous monitoring** of multiple log files
- **Unified log stream** with source identification
- **Configurable refresh rates** and buffer sizes
- **Automatic reconnection** on file rotation

---

## 📊 **Dashboard Features**

### **Three Specialized Dashboards**

#### 1. **🏆 Platform Overview** (`/admin/plugins/security_monitor/platform`)
- Users, challenges, submissions, teams statistics
- Top solvers and challenge solve rates
- Recent activity feed from database

#### 2. **🛡️ Security Monitor** (`/admin/plugins/security_monitor/security`)
- Security events, alerts, threat detection
- Failed login tracking and IP monitoring
- Real-time security statistics

#### 3. **📋 Live Logs** (`/admin/plugins/security_monitor/logs`) - **NEW**
- **Real-time log streaming** from all sources
- **Advanced filtering** by source, level, timestamp
- **Full-text search** across all log entries
- **Live statistics** and error tracking

### **Live Logs Dashboard Features**
```javascript
// Real-time capabilities
✅ WebSocket streaming (with polling fallback)
✅ Auto-scroll with pause/resume
✅ Source filtering (CTFd, Nginx, Docker, System)
✅ Log level filtering (DEBUG, INFO, WARNING, ERROR, CRITICAL)
✅ Full-text search with highlighting
✅ Export logs to JSON
✅ Live statistics (logs/minute, error counts)
✅ Connection status monitoring
```

---

## 🔧 **Technical Implementation**

### **Core Components**

#### **1. Log Manager** (`log_manager.py`)
```python
class LogStreamer:
    - Real-time file monitoring
    - Log rotation handling
    - Multi-format parsing
    - Buffer management (last 1000 entries)
    - Statistics tracking
```

#### **2. WebSocket Handler** (`websocket_handler.py`)
```python
class LogWebSocketHandler:
    - Real-time browser streaming
    - Client filtering and search
    - Connection management
    - Fallback to polling
```

#### **3. Log Parsers** 
```python
LogParser.parse_ctfd_log()      # CTFd application logs
LogParser.parse_nginx_access()  # Web server access logs
LogParser.parse_docker_log()    # Container logs
LogParser.default_parse()       # Generic log format
```

### **API Endpoints**
- `/api/logs` - Get log data with filtering
- `/api/log-sources` - List available log sources
- `/api/platform-stats` - Platform statistics
- `/api/metrics` - Security metrics
- `/api/prometheus` - Prometheus metrics export

---

## 📁 **Log Categories Monitored**

### **Authentication Events**
```
✅ Login attempts (successful/failed)
✅ User registrations
✅ Session management
✅ Password changes
✅ Admin actions
```

### **Platform Activity**
```
✅ Challenge submissions
✅ File uploads/downloads
✅ Scoreboard updates
✅ Team management
✅ Configuration changes
```

### **Security Events**
```
✅ Rate limiting triggers
✅ Suspicious IP activity
✅ Multiple failed attempts
✅ SQL injection attempts
✅ XSS attack detection
```

### **System Events**
```
✅ Application startup/shutdown
✅ Database connections
✅ File system access
✅ Memory/CPU usage
✅ Error conditions
```

### **Container Monitoring** (if Docker available)
```
✅ Container status (up/down)
✅ Resource usage
✅ Container logs
✅ Network activity
```

---

## 🚀 **Usage Instructions**

### **1. Access the Dashboards**
1. Start CTFd server
2. Login as admin
3. Navigate to Admin Panel
4. Use the menu items:
   - **"Platform Overview"** - CTF statistics
   - **"Security Monitor"** - Security events  
   - **"Live Logs"** - Real-time log streaming

### **2. Generate Live Log Data**
```bash
# Run the log generator for testing
python generate_live_logs.py

# This creates realistic log entries in logs/ directory:
# - logs/ctfd_app.log (application logs)
# - logs/access.log (web server logs)
# - logs/error.log (error logs)
# - logs/security.log (security events)
```

### **3. Real-time Monitoring**
- **Auto-refresh**: Dashboard updates every 2 seconds
- **Filtering**: Use dropdowns to filter by source/level
- **Search**: Full-text search across all log entries
- **Export**: Download logs as JSON
- **Pause/Resume**: Control log streaming

---

## 📊 **Dashboard Screenshots Simulation**

### **Live Logs Dashboard Layout**
```
┌─────────────────────────────────────────────────────────┐
│ 📋 Real-time Logs Dashboard                            │
│ [Platform] [Security] [Logs] ← Navigation              │
├─────────────────────────────────────────────────────────┤
│ 🎛️ Controls: [Source▼] [Level▼] [Search] [⏸️] [🗑️]    │
├─────────────────────────────────────────────────────────┤
│ Stats: [1,234 Total] [23 Errors] [5 Sources] [45/min]  │
├─────────────────────────────────────────────────────────┤
│ 📄 Live Log Stream (Terminal-style)                    │
│ 10:30:45 [INFO] [ctfd] User 'alice' logged in          │
│ 10:30:46 [WARN] [nginx] Rate limit exceeded 203.0.113  │
│ 10:30:47 [ERROR] [system] Database connection failed   │
│ 10:30:48 [INFO] [docker] Container ctfd_web started    │
│ ... (auto-scrolling, real-time updates)                │
├─────────────────────────────────────────────────────────┤
│ 🔧 Tools: [📥 Export] [📊 Stats] [🚨 Alerts] [📈 Metrics] │
└─────────────────────────────────────────────────────────┘
```

---

## ✅ **System Status**

### **✅ Completed Features**
- ✅ Real-time log file monitoring
- ✅ Multi-source log aggregation  
- ✅ WebSocket streaming (with fallback)
- ✅ Advanced filtering and search
- ✅ Live dashboard with auto-refresh
- ✅ Log parsing for multiple formats
- ✅ Export functionality
- ✅ Statistics tracking
- ✅ Error handling and reconnection
- ✅ Sample log generation
- ✅ Admin menu integration

### **📋 File Structure**
```
CTFd/plugins/security_monitor/
├── log_manager.py              # Core log streaming engine
├── websocket_handler.py        # Real-time WebSocket streaming  
├── routes.py                   # API endpoints and dashboard routes
├── templates/security_monitor/admin/
│   ├── platform_dashboard.html # Platform statistics
│   ├── security_dashboard.html # Security monitoring
│   └── logs_dashboard.html     # Live log streaming ⭐ NEW
└── models.py                   # Database models

Supporting Files:
├── scan_log_sources.py         # Discover available log sources
├── generate_live_logs.py       # Generate realistic test logs
├── test_logging_system.py      # Comprehensive testing
└── logs/                       # Generated log files
    ├── ctfd_app.log           # Application logs
    ├── access.log             # Web server logs
    ├── error.log              # Error logs
    └── security.log           # Security events
```

---

## 🎯 **Ready for Production**

The comprehensive logging system is **production-ready** with:

- **Real log source integration** ✅
- **Live streaming capabilities** ✅  
- **Advanced filtering and search** ✅
- **Multiple dashboard views** ✅
- **Error handling and fallbacks** ✅
- **Performance optimization** ✅
- **Security event detection** ✅

**🚀 Start using it now:**
1. **Start CTFd** 
2. **Go to Admin Panel**
3. **Click "Live Logs"**
4. **Run log generator** for live data
5. **Monitor real-time activity** across all sources

The system provides **centralized visibility** into all CTFd platform activities through **actual log file analysis**, not just database queries!
