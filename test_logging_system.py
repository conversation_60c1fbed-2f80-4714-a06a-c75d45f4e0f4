#!/usr/bin/env python3
"""
Test the comprehensive logging system
"""

import os
import sys
import time
import json
import threading
from datetime import datetime

def test_log_manager():
    """Test the log manager functionality"""
    print("🔧 Testing Log Manager...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, '.')
        
        from CTFd.plugins.security_monitor.log_manager import LogStreamer, LogParser
        
        # Create test log streamer
        streamer = LogStreamer()
        
        # Test parsers
        print("   ✅ LogStreamer imported successfully")
        
        # Test CTFd log parsing
        ctfd_line = "2024-01-15 10:30:45,123 INFO [app.py:45] User login attempt"
        parsed = LogParser.parse_ctfd_log(ctfd_line)
        print(f"   ✅ CTFd parser: {parsed['level']} - {parsed['message'][:30]}...")
        
        # Test Nginx log parsing
        nginx_line = '************* - - [15/Jan/2024:10:30:45 +0000] "GET /login HTTP/1.1" 200 1234'
        parsed = LogParser.parse_nginx_access(nginx_line)
        print(f"   ✅ Nginx parser: {parsed['ip']} - {parsed['status']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing log manager: {e}")
        return False

def test_log_sources():
    """Test log source detection and setup"""
    print("\n📁 Testing Log Sources...")
    
    # Create sample log files
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Create sample logs
    sample_logs = {
        'logs/ctfd_app.log': [
            f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S,000')} INFO [app.py:45] Application started",
            f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S,001')} WARNING [auth.py:123] Failed login attempt"
        ],
        'logs/access.log': [
            f'127.0.0.1 - - [{datetime.now().strftime("%d/%b/%Y:%H:%M:%S +0000")}] "GET /login HTTP/1.1" 200 1234',
            f'************* - - [{datetime.now().strftime("%d/%b/%Y:%H:%M:%S +0000")}] "POST /api/v1/challenges HTTP/1.1" 429 567'
        ],
        'logs/error.log': [
            f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ERROR: Database connection failed",
            f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ERROR: File not found"
        ]
    }
    
    for log_file, lines in sample_logs.items():
        with open(log_file, 'w') as f:
            for line in lines:
                f.write(line + '\n')
        print(f"   ✅ Created {log_file}")
    
    return True

def test_log_streaming():
    """Test real-time log streaming"""
    print("\n🔄 Testing Log Streaming...")
    
    try:
        sys.path.insert(0, '.')
        from CTFd.plugins.security_monitor.log_manager import log_streamer, LogParser
        
        # Add test log sources
        log_streamer.add_file_source("test_ctfd", "logs/ctfd_app.log", LogParser.parse_ctfd_log)
        log_streamer.add_file_source("test_access", "logs/access.log", LogParser.parse_nginx_access)
        
        print(f"   ✅ Added {len(log_streamer.sources)} log sources")
        
        # Test log collection
        received_logs = []
        
        def log_callback(log_entry):
            received_logs.append(log_entry)
        
        log_streamer.subscribe(log_callback)
        
        # Start streaming
        log_streamer.start_streaming()
        print("   ✅ Log streaming started")
        
        # Wait a moment for initial logs to be processed
        time.sleep(2)
        
        # Add a new log entry
        with open('logs/ctfd_app.log', 'a') as f:
            f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S,999')} INFO [test.py:1] Test log entry\n")
        
        # Wait for the new log to be processed
        time.sleep(1)
        
        # Check results
        recent_logs = log_streamer.get_recent_logs(10)
        print(f"   ✅ Retrieved {len(recent_logs)} recent logs")
        
        stats = log_streamer.get_stats()
        print(f"   ✅ Stats: {stats.get('total_entries', 0)} total entries")
        
        # Test search
        search_results = log_streamer.search_logs("test", 5)
        print(f"   ✅ Search found {len(search_results)} results")
        
        log_streamer.stop_streaming()
        print("   ✅ Log streaming stopped")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing log streaming: {e}")
        return False

def test_dashboard_files():
    """Test dashboard template and route files"""
    print("\n📊 Testing Dashboard Files...")
    
    files_to_check = [
        'CTFd/plugins/security_monitor/templates/security_monitor/admin/logs_dashboard.html',
        'CTFd/plugins/security_monitor/log_manager.py',
        'CTFd/plugins/security_monitor/websocket_handler.py'
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size} bytes)")
        else:
            print(f"   ❌ {file_path} - NOT FOUND")
            all_exist = False
    
    return all_exist

def test_api_endpoints():
    """Test API endpoint structure"""
    print("\n🔌 Testing API Endpoints...")
    
    try:
        # Check if routes file has the required endpoints
        routes_file = 'CTFd/plugins/security_monitor/routes.py'
        
        if not os.path.exists(routes_file):
            print("   ❌ Routes file not found")
            return False
        
        with open(routes_file, 'r') as f:
            content = f.read()
        
        required_endpoints = [
            'def logs_dashboard():',
            'def api_logs():',
            'def api_log_sources():',
            'def setup_log_sources():',
            '/logs'
        ]
        
        missing = []
        for endpoint in required_endpoints:
            if endpoint not in content:
                missing.append(endpoint)
        
        if missing:
            print(f"   ❌ Missing endpoints: {', '.join(missing)}")
            return False
        else:
            print("   ✅ All required API endpoints found")
            return True
            
    except Exception as e:
        print(f"   ❌ Error checking API endpoints: {e}")
        return False

def run_live_demo():
    """Run a live demonstration"""
    print("\n🎬 Running Live Demo...")
    print("   Starting log generation in background...")
    
    # Start log generator in background
    import subprocess
    try:
        process = subprocess.Popen([sys.executable, 'generate_live_logs.py'], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        print("   ✅ Log generator started (PID: {})".format(process.pid))
        print("   📁 Check logs/ directory for real-time log files")
        print("   ⏱️  Logs will be generated for 10 seconds...")
        
        # Let it run for 10 seconds
        time.sleep(10)
        
        # Stop the process
        process.terminate()
        process.wait(timeout=5)
        
        print("   ✅ Log generation stopped")
        
        # Check generated files
        if os.path.exists('logs'):
            log_files = os.listdir('logs')
            print(f"   📄 Generated files: {', '.join(log_files)}")
            
            # Show sample content
            for log_file in log_files[:2]:  # Show first 2 files
                file_path = os.path.join('logs', log_file)
                if os.path.exists(file_path):
                    with open(file_path, 'r') as f:
                        lines = f.readlines()
                    print(f"   📝 {log_file}: {len(lines)} lines")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error running demo: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Comprehensive Logging System")
    print("=" * 50)
    
    tests = [
        ("Log Manager", test_log_manager),
        ("Log Sources", test_log_sources),
        ("Log Streaming", test_log_streaming),
        ("Dashboard Files", test_dashboard_files),
        ("API Endpoints", test_api_endpoints),
        ("Live Demo", run_live_demo)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"   ❌ Test '{test_name}' failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📋 Test Results Summary:")
    print("-" * 30)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The logging system is ready.")
        print("\n📚 Next steps:")
        print("   1. Start CTFd server")
        print("   2. Navigate to Admin Panel")
        print("   3. Click 'Live Logs' menu item")
        print("   4. Run 'python generate_live_logs.py' for live data")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
