{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "ctfd_security_alerts_active", "refId": "A"}], "title": "Active Security Alerts", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "ctfd_failed_logins_24h", "refId": "A"}], "title": "Failed <PERSON> (24h)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 20}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "ctfd_rate_limit_violations_1h", "refId": "A"}], "title": "Rate Limit Violations (1h)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "ctfd_security_events_recent", "refId": "A"}], "title": "Security Events (Last Hour)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 5, "options": {"legend": {"displayMode": "list", "placement": "right"}, "pieType": "pie", "tooltip": {"mode": "single"}}, "targets": [{"expr": "ctfd_security_events_by_type", "legendFormat": "{{type}}", "refId": "A"}], "title": "Security Events by Type", "type": "piechart"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 6, "options": {"displayLabels": ["percent"], "legend": {"displayMode": "list", "placement": "right", "values": ["value"]}, "pieType": "donut", "tooltip": {"mode": "single"}}, "targets": [{"expr": "ctfd_security_alerts_by_severity", "legendFormat": "{{severity}}", "refId": "A"}], "title": "Active Alerts by Severity", "type": "piechart"}, {"datasource": "<PERSON>", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 7, "options": {"showLabels": false, "showCommonLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false, "showAbsoluteTime": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "maxLines": 100}, "targets": [{"expr": "{job=\"security\"} |= \"event_type\"", "refId": "A"}], "title": "Security Events Log", "type": "logs"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "IP Address"}, "properties": [{"id": "custom.width", "value": 150}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 8, "options": {"showHeader": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "topk(10, ctfd_security_events_by_ip)", "format": "table", "instant": true, "refId": "A"}], "title": "Top 10 IPs by Security Events", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"ip": "IP Address", "Value": "Event Count"}}}], "type": "table"}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["ctfd", "security", "operations"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Security Operations Dashboard", "uid": "ctfd-security-ops", "version": 1}