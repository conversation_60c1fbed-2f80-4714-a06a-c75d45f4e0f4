#!/usr/bin/env python3
"""
Fix Docker log source errors by removing Docker sources and ensuring clean operation
"""

import sys
import os

def remove_docker_sources():
    """Remove any Docker log sources from the system"""
    
    print("🔧 Fixing Docker log source errors...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, '.')
        
        from CTFd.plugins.security_monitor.log_manager import log_streamer
        
        # Find and remove any Docker sources
        docker_sources = []
        for name, source in list(log_streamer.sources.items()):
            if source.log_type == 'docker' or 'docker' in name.lower():
                docker_sources.append(name)
        
        if docker_sources:
            print(f"   🗑️  Removing {len(docker_sources)} Docker sources:")
            for source_name in docker_sources:
                print(f"      - {source_name}")
                del log_streamer.sources[source_name]
        else:
            print("   ✅ No Docker sources found to remove")
        
        # Add Prometheus source if not already present
        prometheus_sources = [name for name in log_streamer.sources.keys() if 'prometheus' in name.lower()]
        if not prometheus_sources:
            print("   📊 Adding Prometheus metrics source...")
            log_streamer.add_prometheus_source("ctfd_metrics")
        else:
            print(f"   ✅ Prometheus source already exists: {prometheus_sources}")
        
        # List current sources
        print(f"\n📋 Current log sources ({len(log_streamer.sources)}):")
        for name, source in log_streamer.sources.items():
            status = "🟢 Active" if source.is_active else "🔴 Inactive"
            print(f"   {status} {name} ({source.log_type})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error fixing Docker sources: {e}")
        return False

def test_log_streaming():
    """Test that log streaming works without Docker errors"""
    
    print("\n🧪 Testing log streaming...")
    
    try:
        from CTFd.plugins.security_monitor.log_manager import log_streamer
        
        # Start streaming
        log_streamer.start_streaming()
        print("   ✅ Log streaming started successfully")
        
        # Wait a moment
        import time
        time.sleep(2)
        
        # Check for recent logs
        recent_logs = log_streamer.get_recent_logs(5)
        print(f"   📄 Retrieved {len(recent_logs)} recent log entries")
        
        # Show sample logs
        for log in recent_logs[-3:]:
            print(f"      {log['timestamp'][:19]} [{log['level']}] [{log['source']}] {log['message'][:50]}...")
        
        # Stop streaming
        log_streamer.stop_streaming()
        print("   ✅ Log streaming stopped successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing log streaming: {e}")
        return False

def create_prometheus_sample():
    """Create sample Prometheus metrics if none exist"""
    
    print("\n📊 Ensuring Prometheus metrics are available...")
    
    prometheus_file = "logs/prometheus_metrics.log"
    
    if not os.path.exists("logs"):
        os.makedirs("logs")
        print("   📁 Created logs directory")
    
    if not os.path.exists(prometheus_file):
        from datetime import datetime
        import random
        
        with open(prometheus_file, 'w') as f:
            f.write(f"# CTFd Prometheus Metrics - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"ctfd_requests_total {random.randint(1000, 5000)}\n")
            f.write(f"ctfd_active_users {random.randint(10, 100)}\n")
            f.write(f"ctfd_failed_logins_total {random.randint(0, 50)}\n")
            f.write(f"ctfd_response_time_seconds {random.uniform(0.1, 2.0):.3f}\n")
            f.write(f"ctfd_container_status{{container=\"web\"}} 1\n")
            f.write(f"ctfd_container_status{{container=\"db\"}} 1\n")
            f.write(f"ctfd_container_status{{container=\"redis\"}} 0\n")
        
        print(f"   ✅ Created sample Prometheus metrics: {prometheus_file}")
    else:
        print(f"   ✅ Prometheus metrics file already exists: {prometheus_file}")

def main():
    """Main function"""
    
    print("🛠️  Docker Error Fix Script")
    print("=" * 40)
    
    # Step 1: Remove Docker sources
    success1 = remove_docker_sources()
    
    # Step 2: Create Prometheus samples
    create_prometheus_sample()
    
    # Step 3: Test log streaming
    success2 = test_log_streaming()
    
    # Summary
    print("\n📋 Fix Summary:")
    print("-" * 20)
    
    if success1:
        print("   ✅ Docker sources removed successfully")
    else:
        print("   ❌ Failed to remove Docker sources")
    
    if success2:
        print("   ✅ Log streaming tested successfully")
    else:
        print("   ❌ Log streaming test failed")
    
    if success1 and success2:
        print("\n🎉 All Docker errors should be fixed!")
        print("   The logging system now uses Prometheus metrics instead of Docker logs.")
        print("   No more 'docker command not found' errors should occur.")
    else:
        print("\n⚠️  Some issues remain. Check the errors above.")

if __name__ == "__main__":
    main()
