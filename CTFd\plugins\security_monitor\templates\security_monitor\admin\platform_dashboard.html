{% extends "admin/base.html" %}

{% block content %}
<div class="jumbotron">
    <div class="container">
        <h1>🏆 Platform Overview</h1>
        <p>CTF Platform Statistics and Activity</p>
        <div class="btn-group" role="group">
            <a href="{{ url_for('security_monitor.platform_dashboard') }}" class="btn btn-primary">Platform</a>
            <a href="{{ url_for('security_monitor.security_dashboard') }}" class="btn btn-outline-secondary">Security</a>
        </div>
    </div>
</div>

<div class="container">
    <!-- Platform Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-primary">{{ total_users }}</h2>
                    <h5 class="card-title">Total Users</h5>
                    <small class="text-muted">+{{ recent_users }} today</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-success">{{ total_challenges }}</h2>
                    <h5 class="card-title">Challenges</h5>
                    <small class="text-muted">Available</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-info">{{ total_submissions }}</h2>
                    <h5 class="card-title">Total Submissions</h5>
                    <small class="text-muted">+{{ recent_submissions }} today</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-warning">{{ total_teams }}</h2>
                    <h5 class="card-title">Teams</h5>
                    <small class="text-muted">Registered</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Solvers and Challenge Stats -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🥇 Top Solvers</h3>
                </div>
                <div class="card-body">
                    {% if top_solvers %}
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>User</th>
                                <th>Solves</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user_name, solves in top_solvers %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ user_name }}</td>
                                <td><span class="badge badge-success">{{ solves }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">No submissions yet</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📊 Challenge Statistics</h3>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    {% if challenge_stats %}
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Challenge</th>
                                <th>Category</th>
                                <th>Solves</th>
                                <th>Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for name, category, attempts, solves in challenge_stats %}
                            <tr>
                                <td>{{ name[:20] }}{% if name|length > 20 %}...{% endif %}</td>
                                <td><span class="badge badge-secondary">{{ category or 'General' }}</span></td>
                                <td>{{ solves or 0 }}/{{ attempts or 0 }}</td>
                                <td>
                                    {% if attempts and attempts > 0 %}
                                        {% set rate = ((solves or 0) / attempts * 100) | round(1) %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" style="width: {{ rate }}%">{{ rate }}%</div>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">No attempts</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">No challenges available</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Feed -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🔄 Recent Activity (Last 24 Hours)</h3>
                </div>
                <div class="card-body">
                    {% if recent_activity %}
                    <div class="list-group">
                        {% for activity in recent_activity %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <strong>{{ activity.user_name }}</strong>
                                    {% if activity.type == 'correct' %}
                                        <span class="badge badge-success">✓ SOLVED</span>
                                    {% else %}
                                        <span class="badge badge-warning">✗ ATTEMPT</span>
                                    {% endif %}
                                    {{ activity.challenge_name }}
                                </h6>
                                <small>{{ activity.date.strftime('%H:%M:%S') }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No recent activity</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh every 30 seconds
setInterval(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}
