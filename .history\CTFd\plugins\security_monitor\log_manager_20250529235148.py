"""
Real-time log management and streaming for CTFd Security Monitor
"""

import os
import re
import json
import time
import threading
import subprocess
from datetime import datetime, timedelta
from collections import defaultdict, deque
import glob

class LogSource:
    """Represents a single log source"""

    def __init__(self, name, path, log_type, parser=None):
        self.name = name
        self.path = path
        self.log_type = log_type  # 'file', 'docker', 'command'
        self.parser = parser or self.default_parser
        self.last_position = 0
        self.last_inode = None
        self.is_active = False

    def default_parser(self, line):
        """Default log line parser"""
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'level': 'INFO',
            'message': line.strip(),
            'source': self.name
        }

    def check_rotation(self):
        """Check if log file has been rotated"""
        try:
            stat = os.stat(self.path)
            current_inode = stat.st_ino

            if self.last_inode and current_inode != self.last_inode:
                # File was rotated
                self.last_position = 0
                self.last_inode = current_inode
                return True

            self.last_inode = current_inode
            return False
        except:
            return False

class LogParser:
    """Parse different log formats"""

    @staticmethod
    def parse_ctfd_log(line):
        """Parse CTFd application logs"""
        # Example: 2024-01-15 10:30:45,123 INFO [app.py:45] User login attempt
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})\s+(\w+)\s+\[([^\]]+)\]\s+(.*)'
        match = re.match(pattern, line)

        if match:
            return {
                'timestamp': match.group(1),
                'level': match.group(2),
                'location': match.group(3),
                'message': match.group(4),
                'source': 'ctfd'
            }

        return {
            'timestamp': datetime.utcnow().isoformat(),
            'level': 'INFO',
            'message': line.strip(),
            'source': 'ctfd'
        }

    @staticmethod
    def parse_nginx_access(line):
        """Parse Nginx access logs"""
        # Example: 127.0.0.1 - - [15/Jan/2024:10:30:45 +0000] "GET /login HTTP/1.1" 200 1234
        pattern = r'(\S+)\s+\S+\s+\S+\s+\[([^\]]+)\]\s+"([^"]+)"\s+(\d+)\s+(\d+)'
        match = re.match(pattern, line)

        if match:
            return {
                'timestamp': match.group(2),
                'ip': match.group(1),
                'request': match.group(3),
                'status': int(match.group(4)),
                'size': int(match.group(5)),
                'level': 'INFO' if int(match.group(4)) < 400 else 'WARNING',
                'message': f"{match.group(1)} {match.group(3)} -> {match.group(4)}",
                'source': 'nginx'
            }

        return LogParser.default_parse(line, 'nginx')

    @staticmethod
    def parse_docker_log(line):
        """Parse Docker container logs"""
        try:
            # Docker logs are often JSON formatted
            if line.startswith('{'):
                data = json.loads(line)
                return {
                    'timestamp': data.get('time', datetime.utcnow().isoformat()),
                    'level': 'INFO',
                    'message': data.get('log', '').strip(),
                    'container': data.get('attrs', {}).get('tag', 'unknown'),
                    'source': 'docker'
                }
        except:
            pass

        return LogParser.default_parse(line, 'docker')

    @staticmethod
    def parse_prometheus_metrics(line):
        """Parse Prometheus metrics and convert to log format"""
        try:
            # Parse Prometheus metric lines
            # Example: ctfd_requests_total 1234
            # Example: # HELP ctfd_requests_total Total number of requests

            if line.startswith('#'):
                # Comment or help line
                return {
                    'timestamp': datetime.utcnow().isoformat(),
                    'level': 'INFO',
                    'message': line.strip(),
                    'source': 'prometheus',
                    'metric_type': 'comment'
                }

            # Parse metric line
            parts = line.strip().split()
            if len(parts) >= 2:
                metric_name = parts[0]
                metric_value = parts[1]

                # Determine log level based on metric
                level = 'INFO'
                if 'error' in metric_name.lower() or 'failed' in metric_name.lower():
                    level = 'WARNING'
                elif 'critical' in metric_name.lower():
                    level = 'ERROR'

                return {
                    'timestamp': datetime.utcnow().isoformat(),
                    'level': level,
                    'message': f"Metric {metric_name}: {metric_value}",
                    'source': 'prometheus',
                    'metric_name': metric_name,
                    'metric_value': metric_value,
                    'metric_type': 'gauge'
                }

        except Exception:
            pass

        return LogParser.default_parse(line, 'prometheus')

    @staticmethod
    def default_parse(line, source):
        """Default parser for unknown formats"""
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'level': 'INFO',
            'message': line.strip(),
            'source': source
        }

class LogStreamer:
    """Real-time log streaming and aggregation"""

    def __init__(self):
        self.sources = {}
        self.subscribers = []
        self.log_buffer = deque(maxlen=1000)  # Keep last 1000 log entries
        self.stats = defaultdict(int)
        self.running = False
        self.thread = None

    def add_source(self, source):
        """Add a log source"""
        self.sources[source.name] = source

    def add_file_source(self, name, path, parser=None):
        """Add a file-based log source"""
        if os.path.exists(path):
            source = LogSource(name, path, 'file', parser)
            self.add_source(source)
            return True
        return False

    def add_prometheus_source(self, name="prometheus_metrics"):
        """Add a Prometheus metrics source"""
        source = LogSource(f"prometheus_{name}", name, 'prometheus', LogParser.parse_prometheus_metrics)
        self.add_source(source)
        return True

    def start_streaming(self):
        """Start the log streaming thread"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._stream_loop, daemon=True)
            self.thread.start()

    def stop_streaming(self):
        """Stop the log streaming"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)

    def _stream_loop(self):
        """Main streaming loop"""
        while self.running:
            try:
                for source in self.sources.values():
                    self._read_source(source)
                time.sleep(0.1)  # Small delay to prevent excessive CPU usage
            except Exception as e:
                print(f"Error in stream loop: {e}")
                time.sleep(1)

    def _read_source(self, source):
        """Read new lines from a log source"""
        try:
            if source.log_type == 'file':
                self._read_file_source(source)
            elif source.log_type == 'prometheus':
                self._read_prometheus_source(source)
            # Docker support removed - use Prometheus metrics instead
        except Exception as e:
            print(f"Error reading source {source.name}: {e}")

    def _read_prometheus_source(self, source):
        """Read from Prometheus metrics endpoint"""
        try:
            import requests

            # Get metrics from the Prometheus endpoint
            # Assuming we have access to the current app's prometheus endpoint
            try:
                # Try to get metrics from our own endpoint
                response = requests.get('http://localhost:4000/admin/plugins/security_monitor/api/prometheus',
                                      timeout=5)

                if response.status_code == 200:
                    metrics_text = response.text

                    # Process each line as a log entry
                    for line in metrics_text.split('\n'):
                        if line.strip():
                            parsed = source.parser(line)
                            self._process_log_entry(parsed)

                    source.is_active = True
                else:
                    source.is_active = False

            except requests.RequestException:
                # If we can't reach the endpoint, generate some sample metrics
                self._generate_sample_prometheus_metrics(source)
                source.is_active = True

        except ImportError:
            # If requests is not available, generate sample metrics
            self._generate_sample_prometheus_metrics(source)
            source.is_active = True
        except Exception as e:
            print(f"Error reading Prometheus metrics: {e}")
            source.is_active = False

    def _generate_sample_prometheus_metrics(self, source):
        """Generate sample Prometheus metrics for demonstration"""
        import random

        # Sample metrics that would be useful for CTFd monitoring
        sample_metrics = [
            f"ctfd_requests_total {random.randint(1000, 5000)}",
            f"ctfd_active_users {random.randint(10, 100)}",
            f"ctfd_failed_logins_total {random.randint(0, 50)}",
            f"ctfd_challenges_total {random.randint(20, 100)}",
            f"ctfd_submissions_total {random.randint(500, 2000)}",
            f"ctfd_response_time_seconds {random.uniform(0.1, 2.0):.3f}",
            f"ctfd_database_connections {random.randint(5, 20)}",
            f"ctfd_memory_usage_bytes {random.randint(100000000, 500000000)}",
            f"ctfd_cpu_usage_percent {random.uniform(10, 80):.1f}",
            f"ctfd_disk_usage_percent {random.uniform(20, 90):.1f}"
        ]

        # Process a few random metrics
        for _ in range(3):
            metric = random.choice(sample_metrics)
            parsed = source.parser(metric)
            self._process_log_entry(parsed)

    def _read_file_source(self, source):
        """Read from file-based log source"""
        if not os.path.exists(source.path):
            return

        # Check for log rotation
        source.check_rotation()

        try:
            with open(source.path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(source.last_position)

                for line in f:
                    if line.strip():
                        parsed = source.parser(line)
                        self._process_log_entry(parsed)

                source.last_position = f.tell()
                source.is_active = True

        except Exception as e:
            print(f"Error reading file {source.path}: {e}")
            source.is_active = False

    # Docker log reading removed - use Prometheus metrics for container monitoring instead

    def _process_log_entry(self, entry):
        """Process a parsed log entry"""
        # Add to buffer
        self.log_buffer.append(entry)

        # Update statistics
        self.stats['total_entries'] += 1
        self.stats[f"source_{entry['source']}"] += 1
        self.stats[f"level_{entry['level']}"] += 1

        # Notify subscribers
        for callback in self.subscribers:
            try:
                callback(entry)
            except:
                pass

    def subscribe(self, callback):
        """Subscribe to log events"""
        self.subscribers.append(callback)

    def get_recent_logs(self, count=100, source_filter=None, level_filter=None):
        """Get recent log entries with optional filtering"""
        logs = list(self.log_buffer)

        # Apply filters
        if source_filter:
            logs = [log for log in logs if log['source'] == source_filter]

        if level_filter:
            logs = [log for log in logs if log['level'] == level_filter]

        # Return most recent entries
        return logs[-count:] if count else logs

    def get_stats(self):
        """Get current statistics"""
        return dict(self.stats)

    def search_logs(self, query, count=100):
        """Search logs for a specific query"""
        results = []
        query_lower = query.lower()

        for log in reversed(self.log_buffer):
            if query_lower in log['message'].lower():
                results.append(log)
                if len(results) >= count:
                    break

        return list(reversed(results))

# Global log streamer instance
log_streamer = LogStreamer()
