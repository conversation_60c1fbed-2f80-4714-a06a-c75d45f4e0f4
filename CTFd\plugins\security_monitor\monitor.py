"""
Core monitoring logic for the Security Monitor plugin
"""

from datetime import datetime, timedelta
from CTFd.models import db, Users, Challenges, Submissions
from .models import SecurityEvent, SecurityAlert
from .alerts import AlertManager
import json


class SecurityMonitor:
    """Main security monitoring class"""

    def __init__(self):
        self.alert_manager = AlertManager()
        self.thresholds = {
            'failed_logins': 10,
            'rate_limit_violations': 20,
            'container_spawns': 15,
            'flag_submissions': 100
        }

    def log_event(self, event_type, user_id=None, ip=None, details=None):
        """Log a security event to the database"""
        try:
            event = SecurityEvent(
                event_type=event_type,
                user_id=user_id,
                ip_address=ip,
                details=details or {}
            )
            db.session.add(event)
            db.session.commit()

            # Check if this event triggers any alerts
            self.check_alerts(event_type, user_id, ip)

        except Exception as e:
            # Log error but don't crash
            print(f"Error logging security event: {e}")
            db.session.rollback()

    def check_alerts(self, event_type, user_id, ip):
        """Check if recent events should trigger alerts"""
        try:
            # Check for excessive failed logins
            if event_type == 'failed_login':
                count = SecurityEvent.query.filter_by(
                    event_type='failed_login',
                    ip_address=ip
                ).filter(
                    SecurityEvent.timestamp > datetime.utcnow() - timedelta(minutes=5)
                ).count()

                if count >= self.thresholds['failed_logins']:
                    self.alert_manager.create_alert(
                        alert_type='excessive_failed_logins',
                        severity='warning',
                        title=f'Excessive Failed Logins from {ip}',
                        message=f'IP {ip} has {count} failed login attempts in the last 5 minutes',
                        ip_address=ip,
                        user_id=user_id
                    )

            # Check for rate limit violations
            elif event_type == 'rate_limit_exceeded':
                count = SecurityEvent.query.filter_by(
                    event_type='rate_limit_exceeded',
                    ip_address=ip
                ).filter(
                    SecurityEvent.timestamp > datetime.utcnow() - timedelta(minutes=10)
                ).count()

                if count >= self.thresholds['rate_limit_violations']:
                    self.alert_manager.create_alert(
                        alert_type='excessive_rate_limit_violations',
                        severity='critical',
                        title=f'Possible DDoS from {ip}',
                        message=f'IP {ip} has exceeded rate limits {count} times in the last 10 minutes',
                        ip_address=ip
                    )

        except Exception as e:
            print(f"Error checking alerts: {e}")

    def handle_failed_login(self, ip, username):
        """Handle a failed login attempt"""
        self.log_event('failed_login', ip=ip, details={'username': username})

    def handle_rate_limit_exceeded(self, ip, endpoint, count):
        """Handle rate limit exceeded"""
        self.log_event('rate_limit_exceeded', ip=ip, details={
            'endpoint': endpoint,
            'count': count
        })

    def get_dashboard_stats(self):
        """Get statistics for the security dashboard"""
        try:
            now = datetime.utcnow()
            hour_ago = now - timedelta(hours=1)
            day_ago = now - timedelta(days=1)

            # Count recent events
            recent_events = SecurityEvent.query.filter(
                SecurityEvent.timestamp > hour_ago
            ).count()

            # Count unique IPs (active users)
            active_users = db.session.query(SecurityEvent.ip_address).filter(
                SecurityEvent.timestamp > day_ago
            ).distinct().count()

            # Count failed logins
            failed_logins = SecurityEvent.query.filter_by(
                event_type='failed_login'
            ).filter(
                SecurityEvent.timestamp > day_ago
            ).count()

            # Get recent alerts
            recent_alerts = SecurityAlert.query.filter(
                SecurityAlert.resolved == False
            ).order_by(
                SecurityAlert.timestamp.desc()
            ).limit(5).all()

            # Get top IPs by event count
            top_ips = db.session.query(
                SecurityEvent.ip_address,
                db.func.count(SecurityEvent.id).label('count')
            ).filter(
                SecurityEvent.timestamp > day_ago
            ).group_by(
                SecurityEvent.ip_address
            ).order_by(
                db.func.count(SecurityEvent.id).desc()
            ).limit(10).all()

            # Calculate request rate (events per minute)
            request_rate = recent_events / 60.0

            return {
                'recent_events': recent_events,
                'active_users': active_users,
                'failed_logins': failed_logins,
                'recent_alerts': recent_alerts,
                'top_ips': top_ips,
                'request_rate': round(request_rate, 2)
            }

        except Exception as e:
            print(f"Error getting dashboard stats: {e}")
            return {
                'recent_events': 0,
                'active_users': 0,
                'failed_logins': 0,
                'recent_alerts': [],
                'top_ips': [],
                'request_rate': 0
            }

    def log_submission_attempt(self, user_id, challenge_id, ip, is_correct):
        """Log challenge submission attempts"""
        event_type = 'correct_submission' if is_correct else 'failed_submission'
        self.log_event(event_type, user_id=user_id, ip=ip, details={
            'challenge_id': challenge_id,
            'success': is_correct
        })

    def log_user_registration(self, user_id, ip, username):
        """Log new user registrations"""
        self.log_event('user_registration', user_id=user_id, ip=ip, details={
            'username': username
        })

    def log_admin_action(self, admin_id, action, ip, details=None):
        """Log admin actions"""
        self.log_event('admin_action', user_id=admin_id, ip=ip, details={
            'action': action,
            'details': details or {}
        })

    def get_event_counts_by_type(self, hours=24):
        """Get event counts grouped by type"""
        try:
            cutoff = datetime.utcnow() - timedelta(hours=hours)

            counts = db.session.query(
                SecurityEvent.event_type,
                db.func.count(SecurityEvent.id).label('count')
            ).filter(
                SecurityEvent.timestamp > cutoff
            ).group_by(
                SecurityEvent.event_type
            ).all()

            return dict(counts)

        except Exception as e:
            print(f"Error getting event counts: {e}")
            return {}
