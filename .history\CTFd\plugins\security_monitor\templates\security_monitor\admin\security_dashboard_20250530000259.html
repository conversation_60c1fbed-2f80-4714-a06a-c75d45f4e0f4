{% extends "admin/base.html" %}

{% block content %}
<div class="jumbotron">
    <div class="container">
        <h1>🛡️ Security Monitor</h1>
        <p>Security Events, Threats, and Alerts</p>

    </div>
</div>

<div class="container">
    <!-- Alert Summary -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🚨 Active Alerts</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h2 class="text-danger">{{ alert_summary.critical or 0 }}</h2>
                                <p>Critical</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h2 class="text-warning">{{ alert_summary.warning or 0 }}</h2>
                                <p>Warning</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h2 class="text-info">{{ alert_summary.info or 0 }}</h2>
                                <p>Info</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h2>{{ alert_summary.total or 0 }}</h2>
                                <p>Total Active</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-primary">{{ stats.recent_events }}</h2>
                    <h5 class="card-title">Security Events</h5>
                    <small class="text-muted">Last hour</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-danger">{{ stats.failed_logins }}</h2>
                    <h5 class="card-title">Failed Logins</h5>
                    <small class="text-muted">Last 24 hours</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-success">{{ stats.active_users }}</h2>
                    <h5 class="card-title">Active IPs</h5>
                    <small class="text-muted">Unique visitors</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-info">{{ stats.request_rate }}</h2>
                    <h5 class="card-title">Request Rate</h5>
                    <small class="text-muted">Events/minute</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Alerts and Top IPs -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">⚠️ Recent Alerts</h3>
                </div>
                <div class="card-body">
                    {% if stats.recent_alerts %}
                    <div class="list-group">
                        {% for alert in stats.recent_alerts %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    {% if alert.severity == 'critical' %}
                                    <span class="badge badge-danger">{{ alert.severity.upper() }}</span>
                                    {% elif alert.severity == 'warning' %}
                                    <span class="badge badge-warning">{{ alert.severity.upper() }}</span>
                                    {% else %}
                                    <span class="badge badge-info">{{ alert.severity.upper() }}</span>
                                    {% endif %}
                                    {{ alert.title }}
                                </h6>
                                <small>{{ alert.timestamp.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <p class="mb-1">{{ alert.message }}</p>
                            {% if alert.ip_address %}
                            <small>IP: {{ alert.ip_address }}</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No recent alerts</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🎯 Top IPs by Activity</h3>
                </div>
                <div class="card-body">
                    {% if stats.top_ips %}
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>IP Address</th>
                                <th>Events</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ip, count in stats.top_ips %}
                            <tr>
                                <td>{{ ip }}</td>
                                <td><span class="badge badge-primary">{{ count }}</span></td>
                                <td>
                                    <a href="{{ url_for('security_monitor.admin_events', ip=ip) }}" class="btn btn-sm btn-info">View Events</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">No activity recorded</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Event Type Distribution -->
    {% if event_counts %}
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📈 Event Distribution (Last 24 Hours)</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for event_type, count in event_counts.items() %}
                        <div class="col-md-3 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>{{ event_type.replace('_', ' ').title() }}</span>
                                <span class="badge badge-primary badge-pill">{{ count }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🔧 Quick Actions</h3>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('security_monitor.admin_events') }}" class="btn btn-primary">View All Events</a>
                    <a href="{{ url_for('security_monitor.admin_alerts') }}" class="btn btn-warning">Manage Alerts</a>
                    <a href="{{ url_for('security_monitor.admin_config') }}" class="btn btn-secondary">Configure Security</a>
                    <a href="{{ url_for('security_monitor.prometheus_metrics') }}" class="btn btn-info" target="_blank">Prometheus Metrics</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh stats every 30 seconds
setInterval(function() {
    fetch('{{ url_for("security_monitor.api_stats") }}')
        .then(response => response.json())
        .then(data => {
            // Update the stats on the page
            document.querySelector('[data-stat="recent_events"]').textContent = data.recent_events;
            document.querySelector('[data-stat="request_rate"]').textContent = data.request_rate;
            // You can update more elements as needed
        })
        .catch(error => console.error('Error updating stats:', error));
}, 30000);
</script>
{% endblock %}
