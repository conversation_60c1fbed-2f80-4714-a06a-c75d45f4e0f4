#!/usr/bin/env python3
"""
Generate Prometheus-style metrics logs for the logging dashboard
"""

import os
import time
import random
from datetime import datetime

def generate_prometheus_metrics():
    """Generate realistic Prometheus metrics"""
    
    # Ensure logs directory exists
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    metrics_file = 'logs/prometheus_metrics.log'
    
    # Base values that change over time
    base_requests = random.randint(1000, 5000)
    base_users = random.randint(50, 200)
    base_challenges = random.randint(20, 100)
    
    print("🔥 Generating Prometheus metrics logs...")
    print("📊 Simulating CTFd platform metrics")
    
    try:
        for i in range(20):  # Generate 20 metric snapshots
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Generate realistic metrics with some variation
            metrics = [
                f"# HELP ctfd_requests_total Total number of HTTP requests",
                f"# TYPE ctfd_requests_total counter",
                f"ctfd_requests_total {base_requests + random.randint(-100, 500)}",
                "",
                f"# HELP ctfd_active_users Number of active users",
                f"# TYPE ctfd_active_users gauge", 
                f"ctfd_active_users {base_users + random.randint(-20, 50)}",
                "",
                f"# HELP ctfd_failed_logins_total Total failed login attempts",
                f"# TYPE ctfd_failed_logins_total counter",
                f"ctfd_failed_logins_total {random.randint(0, 100)}",
                "",
                f"# HELP ctfd_challenges_total Total number of challenges",
                f"# TYPE ctfd_challenges_total gauge",
                f"ctfd_challenges_total {base_challenges}",
                "",
                f"# HELP ctfd_submissions_total Total challenge submissions",
                f"# TYPE ctfd_submissions_total counter", 
                f"ctfd_submissions_total {random.randint(500, 3000)}",
                "",
                f"# HELP ctfd_response_time_seconds Average response time",
                f"# TYPE ctfd_response_time_seconds gauge",
                f"ctfd_response_time_seconds {random.uniform(0.05, 2.0):.3f}",
                "",
                f"# HELP ctfd_database_connections Active database connections",
                f"# TYPE ctfd_database_connections gauge",
                f"ctfd_database_connections {random.randint(5, 25)}",
                "",
                f"# HELP ctfd_memory_usage_bytes Memory usage in bytes",
                f"# TYPE ctfd_memory_usage_bytes gauge",
                f"ctfd_memory_usage_bytes {random.randint(100000000, 800000000)}",
                "",
                f"# HELP ctfd_cpu_usage_percent CPU usage percentage",
                f"# TYPE ctfd_cpu_usage_percent gauge",
                f"ctfd_cpu_usage_percent {random.uniform(5, 95):.1f}",
                "",
                f"# HELP ctfd_disk_usage_percent Disk usage percentage",
                f"# TYPE ctfd_disk_usage_percent gauge",
                f"ctfd_disk_usage_percent {random.uniform(20, 85):.1f}",
                "",
                f"# HELP ctfd_error_rate_percent Error rate percentage",
                f"# TYPE ctfd_error_rate_percent gauge",
                f"ctfd_error_rate_percent {random.uniform(0, 5):.2f}",
                "",
                f"# HELP ctfd_security_events_total Security events detected",
                f"# TYPE ctfd_security_events_total counter",
                f"ctfd_security_events_total {random.randint(0, 50)}",
                "",
                f"# HELP ctfd_container_status Container status (1=up, 0=down)",
                f"# TYPE ctfd_container_status gauge",
                f"ctfd_container_status{{container=\"web\"}} {random.choice([0, 1, 1, 1])}",
                f"ctfd_container_status{{container=\"db\"}} {random.choice([0, 1, 1, 1])}",
                f"ctfd_container_status{{container=\"redis\"}} {random.choice([0, 1, 1, 1])}",
                "",
                f"# Generated at {timestamp}",
                "---"
            ]
            
            # Write metrics to file
            with open(metrics_file, 'a') as f:
                for metric in metrics:
                    f.write(metric + '\n')
            
            print(f"   📈 Generated metrics snapshot {i+1}/20")
            time.sleep(0.5)  # Small delay between snapshots
            
    except KeyboardInterrupt:
        print("\n⏹️  Stopped by user")
    
    print(f"\n✅ Generated Prometheus metrics in {metrics_file}")
    
    # Show sample content
    if os.path.exists(metrics_file):
        with open(metrics_file, 'r') as f:
            lines = f.readlines()
        print(f"📄 File contains {len(lines)} lines")
        print("\n📊 Sample metrics:")
        
        # Show some sample metrics
        sample_lines = [line.strip() for line in lines if line.strip() and not line.startswith('#') and not line.startswith('---')]
        for line in sample_lines[:10]:
            print(f"   {line}")
        
        if len(sample_lines) > 10:
            print(f"   ... and {len(sample_lines) - 10} more metrics")

def generate_live_prometheus_stream():
    """Generate continuous Prometheus metrics stream"""
    
    print("🔄 Starting live Prometheus metrics stream...")
    print("⏹️  Press Ctrl+C to stop")
    
    metrics_file = 'logs/prometheus_metrics.log'
    
    try:
        while True:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Generate a few key metrics every few seconds
            live_metrics = [
                f"ctfd_requests_total {random.randint(1000, 6000)}",
                f"ctfd_active_users {random.randint(10, 150)}",
                f"ctfd_response_time_seconds {random.uniform(0.1, 3.0):.3f}",
                f"ctfd_cpu_usage_percent {random.uniform(10, 90):.1f}",
                f"ctfd_memory_usage_bytes {random.randint(200000000, 700000000)}",
                f"# Timestamp: {timestamp}"
            ]
            
            with open(metrics_file, 'a') as f:
                for metric in live_metrics:
                    f.write(metric + '\n')
                f.write('\n')
            
            print(f"📊 {timestamp} - Added live metrics")
            time.sleep(5)  # Update every 5 seconds
            
    except KeyboardInterrupt:
        print("\n✅ Live stream stopped")

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--live':
        generate_live_prometheus_stream()
    else:
        generate_prometheus_metrics()

if __name__ == "__main__":
    main()
