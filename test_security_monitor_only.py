#!/usr/bin/env python3
"""
Test that only Security Monitor menu item is available
"""

import os
import sys

def test_menu_registration():
    """Test that only Security Monitor is registered in the menu"""
    
    print("🧪 Testing Security Monitor Menu Registration")
    print("=" * 50)
    
    # Check the __init__.py file
    init_file = "CTFd/plugins/security_monitor/__init__.py"
    
    if not os.path.exists(init_file):
        print("❌ Plugin __init__.py file not found")
        return False
    
    with open(init_file, 'r') as f:
        content = f.read()
    
    # Check what menu items are registered
    menu_registrations = []
    lines = content.split('\n')
    
    for i, line in enumerate(lines):
        if 'register_admin_plugin_menu_bar' in line:
            # Look for the title in the next few lines
            for j in range(i, min(i+5, len(lines))):
                if 'title=' in lines[j]:
                    title_line = lines[j]
                    if "'" in title_line:
                        title = title_line.split("'")[1]
                        menu_registrations.append(title)
                    elif '"' in title_line:
                        title = title_line.split('"')[1]
                        menu_registrations.append(title)
                    break
    
    print(f"📋 Found {len(menu_registrations)} menu registrations:")
    for title in menu_registrations:
        print(f"   - {title}")
    
    # Check if only Security Monitor is registered
    expected = ['Security Monitor']
    if menu_registrations == expected:
        print("✅ PASS: Only Security Monitor is registered")
        return True
    else:
        print(f"❌ FAIL: Expected {expected}, got {menu_registrations}")
        return False

def test_template_navigation():
    """Test that template doesn't have navigation to other dashboards"""
    
    print("\n🧪 Testing Template Navigation")
    print("-" * 30)
    
    template_file = "CTFd/plugins/security_monitor/templates/security_monitor/admin/security_dashboard.html"
    
    if not os.path.exists(template_file):
        print("❌ Security dashboard template not found")
        return False
    
    with open(template_file, 'r') as f:
        content = f.read()
    
    # Check for navigation elements that shouldn't be there
    problematic_elements = [
        'platform_dashboard',
        'Platform Overview',
        'Live Logs',
        'btn-group.*Platform.*Security'
    ]
    
    issues = []
    for element in problematic_elements:
        if element.lower() in content.lower():
            issues.append(element)
    
    if issues:
        print(f"❌ FAIL: Found problematic navigation elements: {issues}")
        return False
    else:
        print("✅ PASS: No problematic navigation elements found")
        return True

def test_route_references():
    """Test that template doesn't reference non-existent routes"""
    
    print("\n🧪 Testing Route References")
    print("-" * 25)
    
    template_file = "CTFd/plugins/security_monitor/templates/security_monitor/admin/security_dashboard.html"
    
    if not os.path.exists(template_file):
        print("❌ Security dashboard template not found")
        return False
    
    with open(template_file, 'r') as f:
        content = f.read()
    
    # Check for the fixed route reference
    if 'security_monitor.prometheus_metrics' in content:
        print("✅ PASS: Prometheus metrics route reference is correct")
        route_ok = True
    else:
        print("❌ FAIL: Prometheus metrics route reference not found or incorrect")
        route_ok = False
    
    # Check that the old incorrect reference is gone
    if 'security_monitor.api_prometheus' in content:
        print("❌ FAIL: Old incorrect route reference still present")
        route_ok = False
    else:
        print("✅ PASS: Old incorrect route reference removed")
    
    return route_ok

def main():
    """Main test function"""
    
    print("🔧 Testing Security Monitor Only Configuration")
    print("=" * 60)
    
    tests = [
        ("Menu Registration", test_menu_registration),
        ("Template Navigation", test_template_navigation),
        ("Route References", test_route_references)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📋 Test Results Summary:")
    print("-" * 30)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Security Monitor only configuration is working.")
        print("\n📚 Expected behavior:")
        print("   - Only 'Security Monitor' appears in admin menu")
        print("   - No navigation tabs within the dashboard")
        print("   - All route references are correct")
        print("   - Error 500 should be resolved")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
