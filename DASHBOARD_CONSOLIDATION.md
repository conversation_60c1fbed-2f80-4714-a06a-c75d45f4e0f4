# Grafana Dashboard Consolidation

## Overview
Consolidated multiple Grafana dashboards into 2 role-based dashboards, removing redundant panels and organizing by user role.

## Changes Made

### Before (5 dashboards):
1. `ctfd-platform-metrics.json` - Mixed platform and security metrics
2. `ctfd-security-monitor.json` - Security-focused dashboard  
3. `ctfd-security-events.json` - Security events with alerts
4. `nginx-logs.json` - Nginx-specific logs
5. `old-ctfd-security.json.bak` - Backup file

### After (2 dashboards):

#### 1. Platform Operations Dashboard
- **File**: `ctfd-platform-metrics.json`
- **Title**: "Platform Operations Dashboard"
- **UID**: `ctfd-platform-ops`
- **Target Users**: Platform administrators, DevOps teams
- **Panels**:
  - Active Users (gauge)
  - Request Rate (time series)
  - Total Challenges (stat)
  - Top 10 Endpoints by Request Count (pie chart)
  - Total Submissions (stat)
  - Average Response Time (time series)

#### 2. Security Operations Dashboard
- **File**: `ctfd-security-monitor.json`
- **Title**: "Security Operations Dashboard"
- **UID**: `ctfd-security-ops`
- **Target Users**: Security teams, SOC analysts
- **Panels**:
  - Active Security Alerts (stat)
  - Failed Logins (24h) (stat)
  - Rate Limit Violations (1h) (stat)
  - Security Events (Last Hour) (stat)
  - Security Events by Type (pie chart)
  - Active Alerts by Severity (donut chart)
  - Security Events Log (logs panel)
  - Top 10 IPs by Security Events (table)

## Metrics Mapping

### Platform Metrics (Available):
- `ctfd_active_users` - Number of unique IPs seen
- `ctfd_requests_total` - Total number of requests
- `ctfd_requests_by_endpoint` - Requests by endpoint
- `ctfd_response_time_seconds` - Average response time
- `ctfd_challenges_total` - Total challenges (needs implementation)
- `ctfd_submissions_total` - Total submissions (needs implementation)

### Security Metrics (Available):
- `ctfd_security_alerts_active` - Number of active security alerts
- `ctfd_failed_logins_24h` - Failed logins in last 24 hours
- `ctfd_rate_limit_violations_1h` - Rate limit violations in last hour
- `ctfd_security_events_recent` - Events in the last hour
- `ctfd_security_events_by_type` - Security events by type
- `ctfd_security_alerts_by_severity` - Active alerts by severity
- `ctfd_security_events_by_ip` - Top IPs by security events

## Configuration Updates

### Dashboard Provisioning
- Updated `dashboards.yaml` to use "CTFd Operations" folder
- Removed redundant dashboard files
- Consolidated into single provisioning configuration

### Refresh Rates
- Platform Dashboard: 30 seconds (less frequent, operational focus)
- Security Dashboard: 10 seconds (more frequent, security monitoring)

## Removed Files
- `ctfd-security-events.json` (consolidated into security ops dashboard)
- `nginx-logs.json` (nginx-specific, not core CTFd functionality)
- `old-ctfd-security.json.bak` (backup file)

## Benefits

1. **Role-Based Access**: Clear separation between platform operations and security monitoring
2. **Reduced Complexity**: From 5 dashboards to 2 focused dashboards
3. **Better Organization**: Panels grouped by functional area
4. **Improved Performance**: Fewer dashboards to load and maintain
5. **Cleaner UI**: Removed panels that don't show meaningful data

## Next Steps

1. **Test Dashboards**: Verify all panels display data correctly
2. **Add Missing Metrics**: Implement `ctfd_challenges_total` and `ctfd_submissions_total`
3. **User Training**: Update documentation for new dashboard structure
4. **Access Control**: Configure Grafana role-based access if needed

## Dashboard Access

- **Platform Operations**: http://localhost:3000/d/ctfd-platform-ops
- **Security Operations**: http://localhost:3000/d/ctfd-security-ops

Both dashboards are organized under the "CTFd Operations" folder in Grafana.
