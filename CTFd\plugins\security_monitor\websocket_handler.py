"""
WebSocket handler for real-time log streaming
"""

import json
import time
from datetime import datetime
from flask import request
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
from .log_manager import log_streamer

class LogWebSocketHandler:
    """Handle WebSocket connections for real-time log streaming"""
    
    def __init__(self, socketio):
        self.socketio = socketio
        self.connected_clients = {}
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup WebSocket event handlers"""
        
        @self.socketio.on('connect', namespace='/logs')
        def handle_connect():
            """Handle client connection"""
            client_id = request.sid
            self.connected_clients[client_id] = {
                'connected_at': datetime.utcnow(),
                'filters': {},
                'room': 'logs'
            }
            
            join_room('logs')
            
            # Send initial log data
            recent_logs = log_streamer.get_recent_logs(50)
            emit('initial_logs', {
                'logs': recent_logs,
                'stats': log_streamer.get_stats(),
                'sources': list(log_streamer.sources.keys())
            })
            
            print(f"Client {client_id} connected to log stream")
        
        @self.socketio.on('disconnect', namespace='/logs')
        def handle_disconnect():
            """Handle client disconnection"""
            client_id = request.sid
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]
            
            leave_room('logs')
            print(f"Client {client_id} disconnected from log stream")
        
        @self.socketio.on('set_filters', namespace='/logs')
        def handle_set_filters(data):
            """Handle filter updates from client"""
            client_id = request.sid
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['filters'] = data
                
                # Send filtered logs
                filtered_logs = self.get_filtered_logs(data)
                emit('filtered_logs', {'logs': filtered_logs})
        
        @self.socketio.on('search_logs', namespace='/logs')
        def handle_search(data):
            """Handle log search requests"""
            query = data.get('query', '')
            count = data.get('count', 100)
            
            if query:
                results = log_streamer.search_logs(query, count)
                emit('search_results', {
                    'query': query,
                    'results': results,
                    'count': len(results)
                })
        
        @self.socketio.on('get_stats', namespace='/logs')
        def handle_get_stats():
            """Handle statistics requests"""
            stats = log_streamer.get_stats()
            emit('stats_update', stats)
        
        # Subscribe to log events
        log_streamer.subscribe(self.broadcast_log_entry)
    
    def get_filtered_logs(self, filters):
        """Get logs based on filters"""
        source_filter = filters.get('source')
        level_filter = filters.get('level')
        count = filters.get('count', 100)
        
        return log_streamer.get_recent_logs(
            count=count,
            source_filter=source_filter,
            level_filter=level_filter
        )
    
    def broadcast_log_entry(self, log_entry):
        """Broadcast new log entry to all connected clients"""
        try:
            self.socketio.emit('new_log', log_entry, room='logs', namespace='/logs')
        except Exception as e:
            print(f"Error broadcasting log entry: {e}")
    
    def broadcast_stats(self):
        """Broadcast updated statistics"""
        try:
            stats = log_streamer.get_stats()
            self.socketio.emit('stats_update', stats, room='logs', namespace='/logs')
        except Exception as e:
            print(f"Error broadcasting stats: {e}")

# Simple fallback for environments without Flask-SocketIO
class SimpleLogHandler:
    """Simple log handler without WebSocket support"""
    
    def __init__(self):
        self.connected_clients = {}
        log_streamer.subscribe(self.log_callback)
    
    def log_callback(self, log_entry):
        """Handle new log entries"""
        # Store in memory for polling-based access
        pass
    
    def get_recent_logs(self, filters=None):
        """Get recent logs for polling-based clients"""
        return log_streamer.get_recent_logs(
            count=filters.get('count', 100) if filters else 100,
            source_filter=filters.get('source') if filters else None,
            level_filter=filters.get('level') if filters else None
        )
    
    def search_logs(self, query, count=100):
        """Search logs"""
        return log_streamer.search_logs(query, count)
    
    def get_stats(self):
        """Get current statistics"""
        return log_streamer.get_stats()

# Try to use WebSocket handler, fallback to simple handler
try:
    from flask_socketio import SocketIO
    websocket_available = True
except ImportError:
    websocket_available = False

def create_log_handler(app):
    """Create appropriate log handler based on available dependencies"""
    if websocket_available:
        try:
            socketio = SocketIO(app, cors_allowed_origins="*")
            return LogWebSocketHandler(socketio), socketio
        except:
            pass
    
    # Fallback to simple handler
    return SimpleLogHandler(), None
