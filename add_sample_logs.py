#!/usr/bin/env python3
"""
Add sample log entries to demonstrate the logging system
"""

import os
from datetime import datetime
import random

def add_sample_logs():
    """Add realistic sample log entries"""
    
    # Sample CTFd logs
    ctfd_entries = [
        "User 'alice' logged in successfully from *************",
        "Challenge 'web_challenge_1' solved by user 'bob'",
        "Failed login attempt for 'hacker' from ************",
        "Admin 'admin' created new challenge 'crypto_puzzle'",
        "Rate limit exceeded for IP ************",
        "User 'charlie' registered from *********",
        "Incorrect submission by 'diana' for challenge 'binary_exploit'",
        "Database connection timeout after 30 seconds",
        "File upload completed for challenge 'forensics_mystery'",
        "Session expired for user 'eve'"
    ]
    
    levels = ['INFO', 'INFO', 'WARNING', 'INFO', 'ERROR', 'INFO', 'DEBUG', 'ERROR', 'INFO', 'WARNING']
    
    # Add to CTFd log
    with open('logs/ctfd_app.log', 'a') as f:
        for i, (entry, level) in enumerate(zip(ctfd_entries, levels)):
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]
            f.write(f"{timestamp} {level} [app.py:{random.randint(10,999)}] {entry}\n")
    
    # Sample access logs
    access_entries = [
        ('*************', 'GET', '/login', 200),
        ('************', 'POST', '/api/v1/challenges', 429),
        ('*********', 'GET', '/scoreboard', 200),
        ('*************', 'POST', '/api/v1/submissions', 200),
        ('************', 'GET', '/admin', 403),
        ('127.0.0.1', 'GET', '/challenges', 200),
        ('*************', 'POST', '/register', 302),
        ('************', 'POST', '/login', 401),
        ('**********', 'GET', '/api/v1/users', 200),
        ('*************', 'GET', '/teams', 200)
    ]
    
    # Add to access log
    with open('logs/access.log', 'a') as f:
        for ip, method, endpoint, status in access_entries:
            timestamp = datetime.now().strftime('%d/%b/%Y:%H:%M:%S +0000')
            size = random.randint(100, 5000)
            f.write(f'{ip} - - [{timestamp}] "{method} {endpoint} HTTP/1.1" {status} {size} "-" "Mozilla/5.0 (CTFd-Client)"\n')
    
    # Sample error logs
    error_entries = [
        "Database query timeout after 30 seconds",
        "Failed to connect to Redis cache",
        "File not found: /uploads/challenge_file.zip",
        "Permission denied accessing /var/log/ctfd.log",
        "Memory usage exceeded 90% threshold"
    ]
    
    # Add to error log
    with open('logs/error.log', 'a') as f:
        for error in error_entries:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"[{timestamp}] ERROR: {error}\n")
    
    # Sample security logs
    security_entries = [
        "Multiple failed login attempts from ************",
        "Rate limiting triggered for ************",
        "Suspicious file upload attempt from *************",
        "SQL injection attempt detected from ************",
        "Brute force attack detected from ************"
    ]
    
    # Add to security log
    with open('logs/security.log', 'a') as f:
        for event in security_entries:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"[{timestamp}] SECURITY: {event}\n")
    
    print("✅ Added sample log entries to all log files:")
    print(f"   📄 CTFd App: {len(ctfd_entries)} entries")
    print(f"   📄 Access: {len(access_entries)} entries") 
    print(f"   📄 Error: {len(error_entries)} entries")
    print(f"   📄 Security: {len(security_entries)} entries")

if __name__ == "__main__":
    add_sample_logs()
