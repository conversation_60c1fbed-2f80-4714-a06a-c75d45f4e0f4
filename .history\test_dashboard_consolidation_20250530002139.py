#!/usr/bin/env python3
"""
Test script to verify the dashboard consolidation is working correctly
"""

import os
import json
import requests
from datetime import datetime

def test_dashboard_files():
    """Test that dashboard files exist and are valid JSON"""
    print("🔍 Testing Dashboard Files...")
    print("=" * 50)
    
    dashboard_dir = "monitoring/grafana/provisioning/dashboards"
    expected_files = [
        "ctfd-platform-metrics.json",
        "ctfd-security-monitor.json",
        "dashboards.yaml"
    ]
    
    removed_files = [
        "ctfd-security-events.json",
        "nginx-logs.json", 
        "old-ctfd-security.json.bak"
    ]
    
    # Check expected files exist
    for file in expected_files:
        file_path = os.path.join(dashboard_dir, file)
        if os.path.exists(file_path):
            print(f"✅ {file} exists")
            
            # Validate JSON files
            if file.endswith('.json'):
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                    print(f"   📄 Valid JSON with {len(data.get('panels', []))} panels")
                    print(f"   📊 Title: {data.get('title', 'Unknown')}")
                    print(f"   🆔 UID: {data.get('uid', 'Unknown')}")
                except json.JSONDecodeError as e:
                    print(f"❌ {file} has invalid JSON: {e}")
        else:
            print(f"❌ {file} missing")
    
    # Check removed files are gone
    print(f"\n🗑️  Checking removed files...")
    for file in removed_files:
        file_path = os.path.join(dashboard_dir, file)
        if not os.path.exists(file_path):
            print(f"✅ {file} successfully removed")
        else:
            print(f"⚠️  {file} still exists (should be removed)")

def test_dashboard_structure():
    """Test the structure of the consolidated dashboards"""
    print(f"\n📊 Testing Dashboard Structure...")
    print("-" * 30)
    
    # Test Platform Operations Dashboard
    platform_file = "monitoring/grafana/provisioning/dashboards/ctfd-platform-metrics.json"
    if os.path.exists(platform_file):
        with open(platform_file, 'r') as f:
            platform_data = json.load(f)
        
        print(f"🏢 Platform Operations Dashboard:")
        print(f"   Title: {platform_data.get('title')}")
        print(f"   UID: {platform_data.get('uid')}")
        print(f"   Panels: {len(platform_data.get('panels', []))}")
        print(f"   Tags: {platform_data.get('tags', [])}")
        
        # Check for platform-specific panels
        panel_titles = [panel.get('title', '') for panel in platform_data.get('panels', [])]
        expected_platform_panels = [
            "Active Users",
            "Request Rate", 
            "Total Challenges",
            "Total Submissions",
            "Average Response Time"
        ]
        
        for expected in expected_platform_panels:
            if any(expected in title for title in panel_titles):
                print(f"   ✅ Has {expected} panel")
            else:
                print(f"   ❌ Missing {expected} panel")
    
    # Test Security Operations Dashboard  
    security_file = "monitoring/grafana/provisioning/dashboards/ctfd-security-monitor.json"
    if os.path.exists(security_file):
        with open(security_file, 'r') as f:
            security_data = json.load(f)
        
        print(f"\n🔒 Security Operations Dashboard:")
        print(f"   Title: {security_data.get('title')}")
        print(f"   UID: {security_data.get('uid')}")
        print(f"   Panels: {len(security_data.get('panels', []))}")
        print(f"   Tags: {security_data.get('tags', [])}")
        
        # Check for security-specific panels
        panel_titles = [panel.get('title', '') for panel in security_data.get('panels', [])]
        expected_security_panels = [
            "Active Security Alerts",
            "Failed Logins",
            "Rate Limit Violations", 
            "Security Events",
            "Top IPs"
        ]
        
        for expected in expected_security_panels:
            if any(expected in title for title in panel_titles):
                print(f"   ✅ Has {expected} panel")
            else:
                print(f"   ❌ Missing {expected} panel")

def test_metrics_availability():
    """Test if the Prometheus metrics endpoint is working"""
    print(f"\n📈 Testing Metrics Availability...")
    print("-" * 30)
    
    try:
        # Try to access the Prometheus metrics endpoint
        response = requests.get("http://localhost:82/admin/monitor/api/prometheus", timeout=5)
        
        if response.status_code == 200:
            print("✅ Prometheus metrics endpoint accessible")
            
            metrics_text = response.text
            
            # Check for platform metrics
            platform_metrics = [
                "ctfd_active_users",
                "ctfd_requests_total", 
                "ctfd_challenges_total",
                "ctfd_submissions_total",
                "ctfd_response_time_seconds"
            ]
            
            print("\n🏢 Platform Metrics:")
            for metric in platform_metrics:
                if metric in metrics_text:
                    print(f"   ✅ {metric}")
                else:
                    print(f"   ❌ {metric} missing")
            
            # Check for security metrics
            security_metrics = [
                "ctfd_security_alerts_active",
                "ctfd_failed_logins_24h",
                "ctfd_rate_limit_violations_1h",
                "ctfd_security_events_recent",
                "ctfd_security_events_by_type"
            ]
            
            print("\n🔒 Security Metrics:")
            for metric in security_metrics:
                if metric in metrics_text:
                    print(f"   ✅ {metric}")
                else:
                    print(f"   ❌ {metric} missing")
                    
        else:
            print(f"❌ Metrics endpoint returned {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"⚠️  Could not connect to metrics endpoint: {e}")
        print("   (This is expected if CTFd is not running)")

def main():
    """Run all tests"""
    print("🧪 Dashboard Consolidation Test")
    print("=" * 50)
    print(f"📅 Test run: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_dashboard_files()
    test_dashboard_structure()
    test_metrics_availability()
    
    print(f"\n🎯 Summary:")
    print("=" * 20)
    print("✅ Dashboard consolidation completed")
    print("📊 2 role-based dashboards created:")
    print("   1. Platform Operations Dashboard (ctfd-platform-ops)")
    print("   2. Security Operations Dashboard (ctfd-security-ops)")
    print()
    print("🔗 Access URLs (when Grafana is running):")
    print("   Platform: http://localhost:3000/d/ctfd-platform-ops")
    print("   Security: http://localhost:3000/d/ctfd-security-ops")

if __name__ == "__main__":
    main()
