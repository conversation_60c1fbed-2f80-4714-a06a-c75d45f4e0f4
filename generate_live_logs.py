#!/usr/bin/env python3
"""
Generate live log entries to simulate real CTFd activity
This script continuously writes to log files to test the real-time dashboard
"""

import os
import time
import random
import threading
from datetime import datetime

class LogGenerator:
    """Generate realistic log entries for testing"""
    
    def __init__(self):
        self.running = False
        self.threads = []
        
        # Sample data for realistic logs
        self.sample_ips = [
            '*************', '*********', '***********', 
            '************', '*************', '**********',
            '127.0.0.1', '*************', '**********'
        ]
        
        self.sample_usernames = [
            'admin', 'alice', 'bob', 'charlie', 'diana', 
            'eve', 'frank', 'grace', 'hacker', 'test'
        ]
        
        self.sample_endpoints = [
            '/login', '/register', '/challenges', '/admin',
            '/api/v1/challenges', '/api/v1/submissions', 
            '/api/v1/users', '/scoreboard', '/teams'
        ]
        
        self.sample_challenges = [
            'web_challenge_1', 'crypto_puzzle', 'binary_exploit',
            'forensics_mystery', 'reverse_engineering', 'pwn_challenge'
        ]
        
        self.log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
        
        # Ensure logs directory exists
        if not os.path.exists('logs'):
            os.makedirs('logs')
    
    def start(self):
        """Start generating logs"""
        if self.running:
            return
        
        self.running = True
        
        # Start different log generators
        self.threads = [
            threading.Thread(target=self.generate_ctfd_logs, daemon=True),
            threading.Thread(target=self.generate_access_logs, daemon=True),
            threading.Thread(target=self.generate_error_logs, daemon=True),
            threading.Thread(target=self.generate_security_events, daemon=True)
        ]
        
        for thread in self.threads:
            thread.start()
        
        print("🚀 Log generation started!")
        print("📁 Writing to logs/ directory")
        print("⏹️  Press Ctrl+C to stop")
    
    def stop(self):
        """Stop generating logs"""
        self.running = False
        print("\n🛑 Stopping log generation...")
    
    def generate_ctfd_logs(self):
        """Generate CTFd application logs"""
        log_file = 'logs/ctfd_app.log'
        
        while self.running:
            try:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]
                level = random.choice(self.log_levels)
                
                # Generate different types of log messages
                message_type = random.choice(['login', 'submission', 'admin', 'error', 'info'])
                
                if message_type == 'login':
                    username = random.choice(self.sample_usernames)
                    ip = random.choice(self.sample_ips)
                    success = random.choice([True, True, True, False])  # 75% success rate
                    
                    if success:
                        message = f"User '{username}' logged in successfully from {ip}"
                        level = 'INFO'
                    else:
                        message = f"Failed login attempt for '{username}' from {ip}"
                        level = 'WARNING'
                
                elif message_type == 'submission':
                    username = random.choice(self.sample_usernames)
                    challenge = random.choice(self.sample_challenges)
                    correct = random.choice([True, False, False])  # 33% success rate
                    
                    if correct:
                        message = f"User '{username}' solved challenge '{challenge}'"
                        level = 'INFO'
                    else:
                        message = f"Incorrect submission by '{username}' for challenge '{challenge}'"
                        level = 'DEBUG'
                
                elif message_type == 'admin':
                    admin = random.choice(['admin', 'moderator'])
                    action = random.choice(['created challenge', 'updated user', 'deleted submission', 'modified settings'])
                    message = f"Admin '{admin}' {action}"
                    level = 'INFO'
                
                elif message_type == 'error':
                    error_types = [
                        'Database connection timeout',
                        'File upload failed',
                        'Challenge validation error',
                        'Session expired',
                        'Rate limit exceeded'
                    ]
                    message = random.choice(error_types)
                    level = 'ERROR'
                
                else:  # info
                    info_messages = [
                        'Application health check passed',
                        'Cache cleared successfully',
                        'Backup completed',
                        'Configuration reloaded',
                        'Maintenance mode disabled'
                    ]
                    message = random.choice(info_messages)
                    level = 'INFO'
                
                log_entry = f"{timestamp} {level} [app.py:{random.randint(10, 999)}] {message}\n"
                
                with open(log_file, 'a') as f:
                    f.write(log_entry)
                
                # Random delay between 0.5 and 3 seconds
                time.sleep(random.uniform(0.5, 3.0))
                
            except Exception as e:
                print(f"Error generating CTFd logs: {e}")
                time.sleep(1)
    
    def generate_access_logs(self):
        """Generate web server access logs"""
        log_file = 'logs/access.log'
        
        while self.running:
            try:
                timestamp = datetime.now().strftime('%d/%b/%Y:%H:%M:%S +0000')
                ip = random.choice(self.sample_ips)
                endpoint = random.choice(self.sample_endpoints)
                method = random.choice(['GET', 'POST', 'PUT', 'DELETE'])
                
                # Generate realistic status codes
                status_weights = [200, 200, 200, 200, 404, 500, 403, 429]
                status = random.choice(status_weights)
                
                size = random.randint(100, 5000)
                user_agent = "Mozilla/5.0 (CTFd-Test-Client)"
                
                log_entry = f'{ip} - - [{timestamp}] "{method} {endpoint} HTTP/1.1" {status} {size} "-" "{user_agent}"\n'
                
                with open(log_file, 'a') as f:
                    f.write(log_entry)
                
                # Faster for access logs
                time.sleep(random.uniform(0.2, 1.5))
                
            except Exception as e:
                print(f"Error generating access logs: {e}")
                time.sleep(1)
    
    def generate_error_logs(self):
        """Generate error logs"""
        log_file = 'logs/error.log'
        
        while self.running:
            try:
                # Generate errors less frequently
                if random.random() < 0.3:  # 30% chance
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    
                    error_messages = [
                        "Database query timeout after 30 seconds",
                        "Failed to connect to Redis cache",
                        "File not found: /uploads/challenge_file.zip",
                        "Permission denied accessing /var/log/ctfd.log",
                        "Memory usage exceeded 90% threshold",
                        "SSL certificate will expire in 7 days",
                        "Disk space low on /var partition (85% full)"
                    ]
                    
                    message = random.choice(error_messages)
                    log_entry = f"[{timestamp}] ERROR: {message}\n"
                    
                    with open(log_file, 'a') as f:
                        f.write(log_entry)
                
                time.sleep(random.uniform(5, 15))  # Less frequent errors
                
            except Exception as e:
                print(f"Error generating error logs: {e}")
                time.sleep(1)
    
    def generate_security_events(self):
        """Generate security-related log events"""
        log_file = 'logs/security.log'
        
        while self.running:
            try:
                if random.random() < 0.2:  # 20% chance
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ip = random.choice(self.sample_ips)
                    
                    security_events = [
                        f"Multiple failed login attempts from {ip}",
                        f"Rate limiting triggered for {ip}",
                        f"Suspicious file upload attempt from {ip}",
                        f"SQL injection attempt detected from {ip}",
                        f"XSS attempt blocked from {ip}",
                        f"Brute force attack detected from {ip}",
                        f"Unusual access pattern from {ip}"
                    ]
                    
                    event = random.choice(security_events)
                    log_entry = f"[{timestamp}] SECURITY: {event}\n"
                    
                    with open(log_file, 'a') as f:
                        f.write(log_entry)
                
                time.sleep(random.uniform(10, 30))  # Security events are rarer
                
            except Exception as e:
                print(f"Error generating security logs: {e}")
                time.sleep(1)

def main():
    """Main function"""
    generator = LogGenerator()
    
    try:
        generator.start()
        
        # Keep running until interrupted
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        generator.stop()
        print("✅ Log generation stopped")

if __name__ == "__main__":
    main()
