#!/usr/bin/env python3
"""
Scan for available log sources in the CTFd environment
"""

import os
import glob
import subprocess
import json
from datetime import datetime

def check_file_readable(filepath):
    """Check if file exists and is readable"""
    try:
        return os.path.exists(filepath) and os.access(filepath, os.R_OK)
    except:
        return False

def get_file_info(filepath):
    """Get file information"""
    try:
        stat = os.stat(filepath)
        return {
            'size': stat.st_size,
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'readable': os.access(filepath, os.R_OK)
        }
    except:
        return {'size': 0, 'modified': None, 'readable': False}

def scan_docker_logs():
    """Scan for Docker container logs"""
    docker_logs = {}
    try:
        # Check if Docker is available
        result = subprocess.run(['docker', 'ps', '--format', 'json'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            containers = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    try:
                        container = json.loads(line)
                        containers.append(container)
                    except:
                        pass

            docker_logs['containers'] = containers
            docker_logs['available'] = True
        else:
            docker_logs['available'] = False
    except:
        docker_logs['available'] = False

    return docker_logs

def scan_system_logs():
    """Scan for system log files"""
    if os.name == 'nt':  # Windows
        system_log_paths = [
            'C:\\Windows\\System32\\LogFiles\\W3SVC1\\*.log',  # IIS logs
            'C:\\inetpub\\logs\\LogFiles\\W3SVC1\\*.log',     # IIS logs alt
            'C:\\ProgramData\\nginx\\logs\\*.log',            # Nginx on Windows
            'C:\\nginx\\logs\\*.log',                         # Nginx alt
            'C:\\Apache24\\logs\\*.log',                      # Apache on Windows
            'C:\\xampp\\apache\\logs\\*.log',                 # XAMPP logs
            'C:\\wamp\\logs\\*.log',                          # WAMP logs
        ]
    else:  # Unix/Linux
        system_log_paths = [
            '/var/log/auth.log',
            '/var/log/syslog',
            '/var/log/messages',
            '/var/log/secure',
            '/var/log/nginx/access.log',
            '/var/log/nginx/error.log',
            '/var/log/apache2/access.log',
            '/var/log/apache2/error.log',
            '/var/log/httpd/access_log',
            '/var/log/httpd/error_log'
        ]

    found_logs = {}
    for pattern in system_log_paths:
        # Handle both direct paths and glob patterns
        if '*' in pattern:
            for path in glob.glob(pattern):
                if check_file_readable(path):
                    found_logs[path] = get_file_info(path)
        else:
            if check_file_readable(pattern):
                found_logs[pattern] = get_file_info(pattern)

    return found_logs

def scan_ctfd_logs():
    """Scan for CTFd application logs"""
    ctfd_logs = {}

    # Check current directory and common log locations
    log_locations = [
        './logs',
        './CTFd/logs',
        '/var/log/CTFd',
        '/opt/CTFd/logs',
        '/app/logs'
    ]

    for location in log_locations:
        if os.path.exists(location):
            log_files = glob.glob(f"{location}/*.log") + glob.glob(f"{location}/*.txt")
            for log_file in log_files:
                if check_file_readable(log_file):
                    ctfd_logs[log_file] = get_file_info(log_file)

    return ctfd_logs

def scan_database_logs():
    """Scan for database log files"""
    db_log_paths = [
        '/var/log/mysql/error.log',
        '/var/log/mysql/mysql.log',
        '/var/log/postgresql/postgresql.log',
        '/var/log/postgres/postgres.log',
        '/var/lib/mysql/*.log',
        '/var/lib/postgresql/*/log/*.log'
    ]

    found_logs = {}
    for pattern in db_log_paths:
        for path in glob.glob(pattern):
            if check_file_readable(path):
                found_logs[path] = get_file_info(path)

    return found_logs

def check_log_permissions():
    """Check current user's log access permissions"""
    try:
        if os.name == 'nt':  # Windows
            import getpass
            user = getpass.getuser()
            return {
                'user': user,
                'groups': ['Users'],  # Simplified for Windows
                'platform': 'Windows'
            }
        else:  # Unix/Linux
            import pwd
            import grp
            user = pwd.getpwuid(os.getuid()).pw_name
            groups = [grp.getgrgid(g).gr_name for g in os.getgroups()]
            return {
                'user': user,
                'groups': groups,
                'uid': os.getuid(),
                'gid': os.getgid(),
                'platform': 'Unix'
            }
    except:
        return {'user': 'unknown', 'groups': [], 'platform': 'unknown'}

def main():
    print("🔍 Scanning for Available Log Sources...")
    print("=" * 50)

    # Check permissions
    permissions = check_log_permissions()
    print(f"👤 Running as: {permissions['user']} on {permissions['platform']}")
    print(f"👥 Groups: {', '.join(permissions['groups'])}")
    print()

    # Scan Docker logs
    print("🐳 Docker Container Logs:")
    print("-" * 25)
    docker_logs = scan_docker_logs()
    if docker_logs['available']:
        if docker_logs.get('containers'):
            for container in docker_logs['containers']:
                print(f"   ✅ {container.get('Names', 'Unknown')} - {container.get('Image', 'Unknown')}")
                print(f"      Status: {container.get('State', 'Unknown')}")
        else:
            print("   ⚠️  Docker available but no containers running")
    else:
        print("   ❌ Docker not available or not accessible")
    print()

    # Scan CTFd logs
    print("📋 CTFd Application Logs:")
    print("-" * 25)
    ctfd_logs = scan_ctfd_logs()
    if ctfd_logs:
        for log_path, info in ctfd_logs.items():
            size_mb = info['size'] / (1024*1024)
            print(f"   ✅ {log_path}")
            print(f"      Size: {size_mb:.2f} MB, Modified: {info['modified']}")
    else:
        print("   ❌ No CTFd log files found")
    print()

    # Scan system logs
    print("🖥️  System Logs:")
    print("-" * 15)
    system_logs = scan_system_logs()
    if system_logs:
        for log_path, info in system_logs.items():
            size_mb = info['size'] / (1024*1024)
            print(f"   ✅ {log_path}")
            print(f"      Size: {size_mb:.2f} MB, Modified: {info['modified']}")
    else:
        print("   ❌ No accessible system logs found")
    print()

    # Scan database logs
    print("🗄️  Database Logs:")
    print("-" * 16)
    db_logs = scan_database_logs()
    if db_logs:
        for log_path, info in db_logs.items():
            size_mb = info['size'] / (1024*1024)
            print(f"   ✅ {log_path}")
            print(f"      Size: {size_mb:.2f} MB, Modified: {info['modified']}")
    else:
        print("   ❌ No accessible database logs found")
    print()

    # Summary
    total_logs = len(ctfd_logs) + len(system_logs) + len(db_logs)
    docker_count = len(docker_logs.get('containers', []))

    print("📊 Summary:")
    print("-" * 10)
    print(f"   CTFd Logs: {len(ctfd_logs)}")
    print(f"   System Logs: {len(system_logs)}")
    print(f"   Database Logs: {len(db_logs)}")
    print(f"   Docker Containers: {docker_count}")
    print(f"   Total Log Sources: {total_logs + docker_count}")

    # Save results
    results = {
        'scan_time': datetime.now().isoformat(),
        'permissions': permissions,
        'docker_logs': docker_logs,
        'ctfd_logs': ctfd_logs,
        'system_logs': system_logs,
        'database_logs': db_logs
    }

    with open('log_sources_scan.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\n💾 Results saved to log_sources_scan.json")

if __name__ == "__main__":
    main()
