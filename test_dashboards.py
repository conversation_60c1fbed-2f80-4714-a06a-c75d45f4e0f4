#!/usr/bin/env python3
"""
Test script to verify the dashboard structure and routes
"""

import os
import sys

def check_file_exists(filepath, description):
    """Check if a file exists and report"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} - NOT FOUND")
        return False

def check_template_content(filepath, required_elements):
    """Check if template contains required elements"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing = []
        for element in required_elements:
            if element not in content:
                missing.append(element)
        
        if missing:
            print(f"⚠️  Template {filepath} missing: {', '.join(missing)}")
            return False
        else:
            print(f"✅ Template {filepath} has all required elements")
            return True
    except Exception as e:
        print(f"❌ Error reading {filepath}: {e}")
        return False

def main():
    print("🔍 Testing Dashboard Structure...")
    print("=" * 50)
    
    # Check if route files exist
    routes_file = "CTFd/plugins/security_monitor/routes.py"
    init_file = "CTFd/plugins/security_monitor/__init__.py"
    
    files_ok = True
    files_ok &= check_file_exists(routes_file, "Routes file")
    files_ok &= check_file_exists(init_file, "Init file")
    
    # Check template files
    platform_template = "CTFd/plugins/security_monitor/templates/security_monitor/admin/platform_dashboard.html"
    security_template = "CTFd/plugins/security_monitor/templates/security_monitor/admin/security_dashboard.html"
    
    files_ok &= check_file_exists(platform_template, "Platform dashboard template")
    files_ok &= check_file_exists(security_template, "Security dashboard template")
    
    print("\n📋 Checking Template Content...")
    print("-" * 30)
    
    # Check platform template content
    platform_elements = [
        "Platform Overview",
        "total_users",
        "total_challenges", 
        "total_submissions",
        "Top Solvers",
        "Challenge Statistics"
    ]
    
    security_elements = [
        "Security Monitor",
        "Active Alerts",
        "Security Events",
        "Failed Logins",
        "Recent Alerts",
        "Top IPs"
    ]
    
    if os.path.exists(platform_template):
        check_template_content(platform_template, platform_elements)
    
    if os.path.exists(security_template):
        check_template_content(security_template, security_elements)
    
    print("\n🔗 Checking Route Definitions...")
    print("-" * 30)
    
    if os.path.exists(routes_file):
        with open(routes_file, 'r', encoding='utf-8') as f:
            routes_content = f.read()
        
        required_routes = [
            "def platform_dashboard():",
            "def security_dashboard():",
            "def api_platform_stats():",
            "/platform",
            "/security"
        ]
        
        missing_routes = []
        for route in required_routes:
            if route not in routes_content:
                missing_routes.append(route)
        
        if missing_routes:
            print(f"❌ Missing routes: {', '.join(missing_routes)}")
        else:
            print("✅ All required routes found")
    
    print("\n🎯 SUMMARY:")
    print("=" * 20)
    
    if files_ok:
        print("✅ Dashboard structure looks good!")
        print("\n📊 Two dashboards created:")
        print("   1. Platform Overview - Users, Challenges, Solves")
        print("   2. Security Monitor - Threats, Alerts, Events")
        print("\n🔧 Next steps:")
        print("   - Start CTFd server")
        print("   - Navigate to Admin Panel")
        print("   - Look for 'Platform Overview' and 'Security Monitor' menu items")
    else:
        print("❌ Some files are missing or have issues")
        print("   Please check the file paths and content")

if __name__ == "__main__":
    main()
