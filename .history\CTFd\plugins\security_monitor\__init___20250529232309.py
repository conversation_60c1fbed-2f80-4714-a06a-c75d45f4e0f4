"""
CTFd Security Monitor Plugin
Provides security monitoring and alerting for the CTFd platform
"""

from CTFd.models import db, Users
from CTFd.utils.user import get_ip
from CTFd.plugins import register_admin_plugin_menu_bar
from flask import request, Blueprint
import time
import json
from datetime import datetime
import os

# Import our modules
from .monitor import SecurityMonitor
from .routes import security_monitor_bp
from .models import create_tables

# Simple in-memory rate limiter and metrics
rate_limit_cache = {}
alert_log = []
_app = None  # Will be set during load()

# Metrics for platform dashboard
platform_metrics = {
    'requests_total': 0,
    'requests_by_endpoint': {},
    'active_users': set(),
    'failed_logins_total': 0,
    'response_times': [],
    'request_timestamps': []
}

def load(app):
    """Initialize the security monitoring plugin"""

    # Create database tables
    create_tables(app, db)

    # Initialize the security monitor
    app.security_monitor = SecurityMonitor()

    # Store platform metrics and alert log in app for access from routes
    app.platform_metrics = platform_metrics
    app.alert_log = alert_log

    # Store app reference for log function
    global _app
    _app = app

    # Register the blueprint with the app
    app.register_blueprint(security_monitor_bp)

    # Register admin menu item - THIS IS CRITICAL FOR ADMIN PANEL ACCESS
    register_admin_plugin_menu_bar(
        title='Security Monitor',
        route='/admin/plugins/security_monitor/dashboard'
    )

    # Create log directory if it doesn't exist
    log_dir = "/var/log/CTFd"
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
        except:
            log_dir = "./logs"  # Fallback to local directory
            if not os.path.exists(log_dir):
                try:
                    os.makedirs(log_dir)
                except:
                    pass  # Fail silently if we can't create logs

    @app.before_request
    def security_check():
        """Monitor each request for suspicious activity"""
        ip = get_ip()
        endpoint = request.endpoint or request.path

        # Skip static files and admin panel itself
        if endpoint and (endpoint.startswith('static') or endpoint.startswith('themes')):
            return

        # Track request start time for response time calculation
        request._start_time = time.time()

        # Update platform metrics
        platform_metrics['requests_total'] += 1
        platform_metrics['requests_by_endpoint'][endpoint] = platform_metrics['requests_by_endpoint'].get(endpoint, 0) + 1
        platform_metrics['active_users'].add(ip)
        platform_metrics['request_timestamps'].append(time.time())

        # Keep only last 1000 timestamps for rate calculation
        if len(platform_metrics['request_timestamps']) > 1000:
            platform_metrics['request_timestamps'] = platform_metrics['request_timestamps'][-1000:]

        # Rate limiting check
        key = f"{ip}:{endpoint}"
        now = time.time()

        # Clean old entries (older than 60 seconds)
        if key in rate_limit_cache:
            rate_limit_cache[key] = [t for t in rate_limit_cache[key] if now - t < 60]

        # Add current request
        rate_limit_cache.setdefault(key, []).append(now)

        # Check thresholds
        request_count = len(rate_limit_cache[key])

        # Different thresholds for different endpoints
        thresholds = {
            'auth.login': 5,
            'auth.register': 3,
            'challenges.attempt': 30,
            'api.challenges_attempt': 30,
            'default': 60
        }

        threshold = thresholds.get(endpoint, thresholds['default'])

        if request_count > threshold:
            log_security_event('rate_limit_exceeded', ip, {
                'endpoint': endpoint,
                'count': request_count,
                'threshold': threshold
            })

            # Let the security monitor handle this
            if hasattr(app, 'security_monitor'):
                app.security_monitor.handle_rate_limit_exceeded(ip, endpoint, request_count)

    @app.after_request
    def log_response(response):
        """Log failed authentication attempts and track response times"""
        # Calculate response time
        if hasattr(request, '_start_time'):
            response_time = time.time() - request._start_time
            platform_metrics['response_times'].append(response_time)

            # Keep only last 1000 response times
            if len(platform_metrics['response_times']) > 1000:
                platform_metrics['response_times'] = platform_metrics['response_times'][-1000:]

        # Check for failed login
        if request.endpoint == 'auth.login' and response.status_code in [401, 403]:
            platform_metrics['failed_logins_total'] += 1

            log_security_event('failed_login', get_ip(), {
                'username': request.form.get('name', 'unknown')
            })

            # Let the security monitor handle this
            if hasattr(app, 'security_monitor'):
                app.security_monitor.handle_failed_login(get_ip(), request.form.get('name', 'unknown'))

        # Log successful logins
        elif request.endpoint == 'auth.login' and response.status_code == 302:
            log_security_event('successful_login', get_ip(), {
                'username': request.form.get('name', 'unknown')
            })

        # Log user registrations
        elif request.endpoint == 'auth.register' and response.status_code == 302:
            log_security_event('user_registration', get_ip(), {
                'username': request.form.get('name', 'unknown'),
                'email': request.form.get('email', 'unknown')
            })

        # Log challenge submissions
        elif request.endpoint in ['challenges.attempt', 'api.challenges_attempt'] and response.status_code == 200:
            try:
                # Try to determine if submission was correct based on response
                if hasattr(response, 'data'):
                    response_data = response.get_data(as_text=True)
                    is_correct = 'correct' in response_data.lower()

                    log_security_event('challenge_submission', get_ip(), {
                        'challenge_id': request.form.get('challenge_id', 'unknown'),
                        'success': is_correct
                    })
            except:
                pass  # Fail silently for submission logging

        return response

def log_security_event(event_type, ip, details):
    """Log security events to file and memory"""
    log_entry = {
        'timestamp': datetime.utcnow().isoformat(),
        'event_type': event_type,
        'ip': ip,
        'details': details
    }

    # Add to in-memory log for admin panel
    alert_log.append(log_entry)
    if len(alert_log) > 100:  # Keep only last 100 alerts
        alert_log.pop(0)

    # Log to database using the security monitor
    if _app and hasattr(_app, 'security_monitor'):
        _app.security_monitor.log_event(event_type, ip=ip, details=details)

    # Write to file
    try:
        log_file = "/var/log/CTFd/security.log"
        if not os.path.exists("/var/log/CTFd"):
            log_file = "./logs/security.log"

        with open(log_file, 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
    except Exception as e:
        # Fail silently - we don't want logging errors to break the app
        pass
