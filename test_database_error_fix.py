#!/usr/bin/env python3
"""
Test that database errors are fixed by removing problematic routes
"""

import os
import sys

def test_routes_file():
    """Test that problematic database queries are removed"""
    
    print("🧪 Testing Database Error Fix")
    print("=" * 40)
    
    routes_file = "CTFd/plugins/security_monitor/routes.py"
    
    if not os.path.exists(routes_file):
        print("❌ Routes file not found")
        return False
    
    with open(routes_file, 'r') as f:
        content = f.read()
    
    # Check that problematic database queries are removed
    problematic_patterns = [
        'top_solvers = db.session.query(',
        'challenge_stats = db.session.query(',
        'recent_activity = db.session.query(',
        'func.case([(Submissions.type == \'correct\', 1)]',
        'Users.name.label(\'user_name\')',
        'Challenges.name.label(\'challenge_name\')'
    ]
    
    issues = []
    for pattern in problematic_patterns:
        if pattern in content:
            issues.append(pattern)
    
    if issues:
        print(f"❌ FAIL: Found problematic database queries:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ PASS: No problematic database queries found")
    
    # Check that platform route redirects to security
    if 'def platform_dashboard():' in content and 'redirect(url_for(\'security_monitor.security_dashboard\'))' in content:
        print("✅ PASS: Platform route redirects to security dashboard")
    else:
        print("❌ FAIL: Platform route not properly redirecting")
        return False
    
    # Check that platform stats API is removed
    if 'api_platform_stats' not in content:
        print("✅ PASS: Platform stats API removed")
    else:
        print("❌ FAIL: Platform stats API still present")
        return False
    
    return True

def test_menu_configuration():
    """Test that only Security Monitor is in the menu"""
    
    print("\n🧪 Testing Menu Configuration")
    print("-" * 30)
    
    init_file = "CTFd/plugins/security_monitor/__init__.py"
    
    if not os.path.exists(init_file):
        print("❌ Plugin __init__.py file not found")
        return False
    
    with open(init_file, 'r') as f:
        content = f.read()
    
    # Count menu registrations
    menu_count = content.count('register_admin_plugin_menu_bar')
    
    if menu_count == 1:
        print(f"✅ PASS: Only 1 menu item registered")
    else:
        print(f"❌ FAIL: {menu_count} menu items found (should be 1)")
        return False
    
    # Check that only Security Monitor is registered
    if 'Security Monitor' in content and 'Platform Overview' not in content and 'Live Logs' not in content:
        print("✅ PASS: Only Security Monitor menu item present")
        return True
    else:
        print("❌ FAIL: Wrong menu items found")
        return False

def test_template_fixes():
    """Test that template route references are fixed"""
    
    print("\n🧪 Testing Template Fixes")
    print("-" * 25)
    
    template_file = "CTFd/plugins/security_monitor/templates/security_monitor/admin/security_dashboard.html"
    
    if not os.path.exists(template_file):
        print("❌ Security dashboard template not found")
        return False
    
    with open(template_file, 'r') as f:
        content = f.read()
    
    # Check for correct route reference
    if 'security_monitor.prometheus_metrics' in content:
        print("✅ PASS: Prometheus metrics route reference is correct")
        route_ok = True
    else:
        print("❌ FAIL: Prometheus metrics route reference not found")
        route_ok = False
    
    # Check that incorrect reference is gone
    if 'security_monitor.api_prometheus' not in content:
        print("✅ PASS: Old incorrect route reference removed")
    else:
        print("❌ FAIL: Old incorrect route reference still present")
        route_ok = False
    
    # Check that navigation buttons are removed
    if 'btn-group' not in content or 'Platform' not in content:
        print("✅ PASS: Navigation buttons removed")
    else:
        print("❌ FAIL: Navigation buttons still present")
        route_ok = False
    
    return route_ok

def main():
    """Main test function"""
    
    print("🔧 Testing Database Error Fixes")
    print("=" * 50)
    
    tests = [
        ("Routes File", test_routes_file),
        ("Menu Configuration", test_menu_configuration),
        ("Template Fixes", test_template_fixes)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📋 Test Results Summary:")
    print("-" * 30)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Database errors should be fixed.")
        print("\n📚 Expected behavior:")
        print("   - No more Error 500 on platform routes")
        print("   - Platform routes redirect to security dashboard")
        print("   - Only Security Monitor in admin menu")
        print("   - All database queries removed from platform code")
        print("   - Template route references are correct")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
