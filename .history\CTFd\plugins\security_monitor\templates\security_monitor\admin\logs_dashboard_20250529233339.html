{% extends "admin/base.html" %}

{% block content %}
<div class="jumbotron">
    <div class="container">
        <h1>📋 Real-time Logs Dashboard</h1>
        <p>Live log streaming and analysis from multiple sources</p>
        <div class="btn-group" role="group">
            <a href="{{ url_for('security_monitor.platform_dashboard') }}" class="btn btn-outline-secondary">Platform</a>
            <a href="{{ url_for('security_monitor.security_dashboard') }}" class="btn btn-outline-secondary">Security</a>
            <a href="{{ url_for('security_monitor.logs_dashboard') }}" class="btn btn-primary">Logs</a>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Control Panel -->
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>🎛️ Log Controls</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <label>Source Filter:</label>
                            <select id="sourceFilter" class="form-control form-control-sm">
                                <option value="">All Sources</option>
                                <option value="ctfd">CTFd App</option>
                                <option value="nginx">Nginx</option>
                                <option value="docker">Docker</option>
                                <option value="system">System</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label>Log Level:</label>
                            <select id="levelFilter" class="form-control form-control-sm">
                                <option value="">All Levels</option>
                                <option value="DEBUG">Debug</option>
                                <option value="INFO">Info</option>
                                <option value="WARNING">Warning</option>
                                <option value="ERROR">Error</option>
                                <option value="CRITICAL">Critical</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label>Search:</label>
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control form-control-sm" placeholder="Search logs...">
                                <div class="input-group-append">
                                    <button id="searchBtn" class="btn btn-sm btn-primary">🔍</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label>Auto-scroll:</label><br>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="autoScroll" checked>
                                <label class="form-check-label" for="autoScroll">Enabled</label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label>Actions:</label><br>
                            <button id="pauseBtn" class="btn btn-sm btn-warning">⏸️ Pause</button>
                            <button id="clearBtn" class="btn btn-sm btn-danger">🗑️ Clear</button>
                        </div>
                        <div class="col-md-1">
                            <label>Status:</label><br>
                            <span id="connectionStatus" class="badge badge-secondary">Connecting...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="row mb-3">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 id="totalLogs">0</h3>
                    <small>Total Log Entries</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 id="errorCount">0</h3>
                    <small>Errors/Warnings</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 id="activeSources">0</h3>
                    <small>Active Sources</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 id="logsPerMinute">0</h3>
                    <small>Logs/Minute</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Log Display -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>📄 Live Log Stream</h5>
                    <small class="text-muted">Real-time log entries from all sources</small>
                </div>
                <div class="card-body p-0">
                    <div id="logContainer" style="height: 600px; overflow-y: auto; background: #1e1e1e; color: #fff; font-family: 'Courier New', monospace; font-size: 12px;">
                        <div id="logContent" class="p-2">
                            <div class="text-center text-muted p-4">
                                <div class="spinner-border" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                                <p class="mt-2">Connecting to log stream...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export and Analysis Tools -->
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>🔧 Log Analysis Tools</h5>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <button id="exportBtn" class="btn btn-info">📥 Export Logs</button>
                        <button id="statsBtn" class="btn btn-secondary">📊 View Statistics</button>
                        <button id="alertsBtn" class="btn btn-warning">🚨 Setup Alerts</button>
                        <a href="{{ url_for('security_monitor.api_prometheus') }}" target="_blank" class="btn btn-success">📈 Prometheus Metrics</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Log Entry Template -->
<template id="logEntryTemplate">
    <div class="log-entry" data-level="" data-source="">
        <span class="log-timestamp text-muted"></span>
        <span class="log-level badge"></span>
        <span class="log-source badge badge-secondary"></span>
        <span class="log-message"></span>
    </div>
</template>

<style>
.log-entry {
    padding: 2px 0;
    border-bottom: 1px solid #333;
    word-wrap: break-word;
}

.log-entry:hover {
    background-color: #2a2a2a;
}

.log-level.DEBUG { background-color: #6c757d; }
.log-level.INFO { background-color: #17a2b8; }
.log-level.WARNING { background-color: #ffc107; color: #000; }
.log-level.ERROR { background-color: #dc3545; }
.log-level.CRITICAL { background-color: #721c24; }

.log-timestamp {
    font-size: 10px;
    width: 150px;
    display: inline-block;
}

.log-source {
    font-size: 10px;
    margin-right: 5px;
}

.log-message {
    margin-left: 5px;
}

#logContainer {
    border: 1px solid #333;
}

.card-body.p-0 {
    padding: 0 !important;
}
</style>

<script>
class LogDashboard {
    constructor() {
        this.socket = null;
        this.isPaused = false;
        this.autoScroll = true;
        this.logCount = 0;
        this.errorCount = 0;
        this.logsPerMinute = 0;
        this.lastMinuteCount = 0;
        this.logBuffer = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.connectToLogStream();
        this.startStatsTimer();
    }
    
    initializeElements() {
        this.logContainer = document.getElementById('logContainer');
        this.logContent = document.getElementById('logContent');
        this.sourceFilter = document.getElementById('sourceFilter');
        this.levelFilter = document.getElementById('levelFilter');
        this.searchInput = document.getElementById('searchInput');
        this.autoScrollCheck = document.getElementById('autoScroll');
        this.pauseBtn = document.getElementById('pauseBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.connectionStatus = document.getElementById('connectionStatus');
        this.logEntryTemplate = document.getElementById('logEntryTemplate');
    }
    
    setupEventListeners() {
        this.sourceFilter.addEventListener('change', () => this.applyFilters());
        this.levelFilter.addEventListener('change', () => this.applyFilters());
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.searchLogs();
        });
        document.getElementById('searchBtn').addEventListener('click', () => this.searchLogs());
        this.autoScrollCheck.addEventListener('change', (e) => {
            this.autoScroll = e.target.checked;
        });
        this.pauseBtn.addEventListener('click', () => this.togglePause());
        this.clearBtn.addEventListener('click', () => this.clearLogs());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportLogs());
    }
    
    connectToLogStream() {
        // Try WebSocket connection first, fallback to polling
        try {
            if (typeof io !== 'undefined') {
                this.socket = io('/logs');
                this.setupSocketHandlers();
            } else {
                this.setupPolling();
            }
        } catch (e) {
            console.log('WebSocket not available, using polling');
            this.setupPolling();
        }
    }
    
    setupSocketHandlers() {
        this.socket.on('connect', () => {
            this.updateConnectionStatus('Connected', 'success');
        });
        
        this.socket.on('disconnect', () => {
            this.updateConnectionStatus('Disconnected', 'danger');
        });
        
        this.socket.on('initial_logs', (data) => {
            this.logContent.innerHTML = '';
            data.logs.forEach(log => this.addLogEntry(log));
            this.updateStats(data.stats);
        });
        
        this.socket.on('new_log', (log) => {
            if (!this.isPaused) {
                this.addLogEntry(log);
            }
        });
        
        this.socket.on('search_results', (data) => {
            this.displaySearchResults(data.results);
        });
        
        this.socket.on('stats_update', (stats) => {
            this.updateStats(stats);
        });
    }
    
    setupPolling() {
        this.updateConnectionStatus('Polling', 'warning');
        
        // Poll for new logs every 2 seconds
        setInterval(() => {
            if (!this.isPaused) {
                this.fetchLogs();
            }
        }, 2000);
    }
    
    fetchLogs() {
        fetch('/admin/plugins/security_monitor/api/logs')
            .then(response => response.json())
            .then(data => {
                if (data.logs) {
                    // Only add new logs (simple implementation)
                    data.logs.slice(-10).forEach(log => this.addLogEntry(log));
                }
                if (data.stats) {
                    this.updateStats(data.stats);
                }
            })
            .catch(error => {
                console.error('Error fetching logs:', error);
                this.updateConnectionStatus('Error', 'danger');
            });
    }
    
    addLogEntry(log) {
        if (!this.passesFilters(log)) return;
        
        const entry = this.createLogEntry(log);
        this.logContent.appendChild(entry);
        
        // Keep only last 1000 entries for performance
        while (this.logContent.children.length > 1000) {
            this.logContent.removeChild(this.logContent.firstChild);
        }
        
        this.logCount++;
        if (log.level === 'ERROR' || log.level === 'WARNING' || log.level === 'CRITICAL') {
            this.errorCount++;
        }
        
        if (this.autoScroll) {
            this.logContainer.scrollTop = this.logContainer.scrollHeight;
        }
    }
    
    createLogEntry(log) {
        const template = this.logEntryTemplate.content.cloneNode(true);
        const entry = template.querySelector('.log-entry');
        
        entry.dataset.level = log.level;
        entry.dataset.source = log.source;
        
        entry.querySelector('.log-timestamp').textContent = this.formatTimestamp(log.timestamp);
        
        const levelBadge = entry.querySelector('.log-level');
        levelBadge.textContent = log.level;
        levelBadge.className = `log-level badge ${log.level}`;
        
        entry.querySelector('.log-source').textContent = log.source;
        entry.querySelector('.log-message').textContent = log.message;
        
        return entry;
    }
    
    formatTimestamp(timestamp) {
        try {
            const date = new Date(timestamp);
            return date.toLocaleTimeString();
        } catch {
            return timestamp;
        }
    }
    
    passesFilters(log) {
        const sourceFilter = this.sourceFilter.value;
        const levelFilter = this.levelFilter.value;
        
        if (sourceFilter && log.source !== sourceFilter) return false;
        if (levelFilter && log.level !== levelFilter) return false;
        
        return true;
    }
    
    applyFilters() {
        const entries = this.logContent.querySelectorAll('.log-entry');
        entries.forEach(entry => {
            const log = {
                source: entry.dataset.source,
                level: entry.dataset.level
            };
            entry.style.display = this.passesFilters(log) ? 'block' : 'none';
        });
    }
    
    searchLogs() {
        const query = this.searchInput.value.trim();
        if (!query) return;
        
        if (this.socket) {
            this.socket.emit('search_logs', { query: query, count: 100 });
        } else {
            // Fallback search in current logs
            const entries = this.logContent.querySelectorAll('.log-entry');
            entries.forEach(entry => {
                const message = entry.querySelector('.log-message').textContent;
                entry.style.display = message.toLowerCase().includes(query.toLowerCase()) ? 'block' : 'none';
            });
        }
    }
    
    togglePause() {
        this.isPaused = !this.isPaused;
        this.pauseBtn.textContent = this.isPaused ? '▶️ Resume' : '⏸️ Pause';
        this.pauseBtn.className = this.isPaused ? 'btn btn-sm btn-success' : 'btn btn-sm btn-warning';
    }
    
    clearLogs() {
        this.logContent.innerHTML = '';
        this.logCount = 0;
        this.errorCount = 0;
        this.updateDisplayStats();
    }
    
    exportLogs() {
        const logs = Array.from(this.logContent.querySelectorAll('.log-entry')).map(entry => {
            return {
                timestamp: entry.querySelector('.log-timestamp').textContent,
                level: entry.querySelector('.log-level').textContent,
                source: entry.querySelector('.log-source').textContent,
                message: entry.querySelector('.log-message').textContent
            };
        });
        
        const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ctfd_logs_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
    
    updateConnectionStatus(status, type) {
        this.connectionStatus.textContent = status;
        this.connectionStatus.className = `badge badge-${type}`;
    }
    
    updateStats(stats) {
        if (stats) {
            document.getElementById('totalLogs').textContent = stats.total_entries || this.logCount;
            document.getElementById('errorCount').textContent = (stats.level_ERROR || 0) + (stats.level_WARNING || 0) + (stats.level_CRITICAL || 0);
            
            // Count active sources
            const activeSources = Object.keys(stats).filter(key => key.startsWith('source_')).length;
            document.getElementById('activeSources').textContent = activeSources;
        }
        
        this.updateDisplayStats();
    }
    
    updateDisplayStats() {
        document.getElementById('totalLogs').textContent = this.logCount;
        document.getElementById('errorCount').textContent = this.errorCount;
    }
    
    startStatsTimer() {
        setInterval(() => {
            // Calculate logs per minute
            const currentCount = this.logCount;
            this.logsPerMinute = currentCount - this.lastMinuteCount;
            this.lastMinuteCount = currentCount;
            
            document.getElementById('logsPerMinute').textContent = this.logsPerMinute;
        }, 60000); // Update every minute
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new LogDashboard();
});
</script>
{% endblock %}
