"""
Real-time log management and streaming for CTFd Security Monitor
"""

import os
import re
import json
import time
import threading
import subprocess
from datetime import datetime, timedelta
from collections import defaultdict, deque
import glob

class LogSource:
    """Represents a single log source"""
    
    def __init__(self, name, path, log_type, parser=None):
        self.name = name
        self.path = path
        self.log_type = log_type  # 'file', 'docker', 'command'
        self.parser = parser or self.default_parser
        self.last_position = 0
        self.last_inode = None
        self.is_active = False
        
    def default_parser(self, line):
        """Default log line parser"""
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'level': 'INFO',
            'message': line.strip(),
            'source': self.name
        }
    
    def check_rotation(self):
        """Check if log file has been rotated"""
        try:
            stat = os.stat(self.path)
            current_inode = stat.st_ino
            
            if self.last_inode and current_inode != self.last_inode:
                # File was rotated
                self.last_position = 0
                self.last_inode = current_inode
                return True
            
            self.last_inode = current_inode
            return False
        except:
            return False

class LogParser:
    """Parse different log formats"""
    
    @staticmethod
    def parse_ctfd_log(line):
        """Parse CTFd application logs"""
        # Example: 2024-01-15 10:30:45,123 INFO [app.py:45] User login attempt
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})\s+(\w+)\s+\[([^\]]+)\]\s+(.*)'
        match = re.match(pattern, line)
        
        if match:
            return {
                'timestamp': match.group(1),
                'level': match.group(2),
                'location': match.group(3),
                'message': match.group(4),
                'source': 'ctfd'
            }
        
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'level': 'INFO',
            'message': line.strip(),
            'source': 'ctfd'
        }
    
    @staticmethod
    def parse_nginx_access(line):
        """Parse Nginx access logs"""
        # Example: 127.0.0.1 - - [15/Jan/2024:10:30:45 +0000] "GET /login HTTP/1.1" 200 1234
        pattern = r'(\S+)\s+\S+\s+\S+\s+\[([^\]]+)\]\s+"([^"]+)"\s+(\d+)\s+(\d+)'
        match = re.match(pattern, line)
        
        if match:
            return {
                'timestamp': match.group(2),
                'ip': match.group(1),
                'request': match.group(3),
                'status': int(match.group(4)),
                'size': int(match.group(5)),
                'level': 'INFO' if int(match.group(4)) < 400 else 'WARNING',
                'message': f"{match.group(1)} {match.group(3)} -> {match.group(4)}",
                'source': 'nginx'
            }
        
        return LogParser.default_parse(line, 'nginx')
    
    @staticmethod
    def parse_docker_log(line):
        """Parse Docker container logs"""
        try:
            # Docker logs are often JSON formatted
            if line.startswith('{'):
                data = json.loads(line)
                return {
                    'timestamp': data.get('time', datetime.utcnow().isoformat()),
                    'level': 'INFO',
                    'message': data.get('log', '').strip(),
                    'container': data.get('attrs', {}).get('tag', 'unknown'),
                    'source': 'docker'
                }
        except:
            pass
        
        return LogParser.default_parse(line, 'docker')
    
    @staticmethod
    def default_parse(line, source):
        """Default parser for unknown formats"""
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'level': 'INFO',
            'message': line.strip(),
            'source': source
        }

class LogStreamer:
    """Real-time log streaming and aggregation"""
    
    def __init__(self):
        self.sources = {}
        self.subscribers = []
        self.log_buffer = deque(maxlen=1000)  # Keep last 1000 log entries
        self.stats = defaultdict(int)
        self.running = False
        self.thread = None
        
    def add_source(self, source):
        """Add a log source"""
        self.sources[source.name] = source
        
    def add_file_source(self, name, path, parser=None):
        """Add a file-based log source"""
        if os.path.exists(path):
            source = LogSource(name, path, 'file', parser)
            self.add_source(source)
            return True
        return False
    
    def add_docker_source(self, container_name):
        """Add a Docker container log source"""
        source = LogSource(f"docker_{container_name}", container_name, 'docker', LogParser.parse_docker_log)
        self.add_source(source)
        return True
    
    def start_streaming(self):
        """Start the log streaming thread"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._stream_loop, daemon=True)
            self.thread.start()
    
    def stop_streaming(self):
        """Stop the log streaming"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
    
    def _stream_loop(self):
        """Main streaming loop"""
        while self.running:
            try:
                for source in self.sources.values():
                    self._read_source(source)
                time.sleep(0.1)  # Small delay to prevent excessive CPU usage
            except Exception as e:
                print(f"Error in stream loop: {e}")
                time.sleep(1)
    
    def _read_source(self, source):
        """Read new lines from a log source"""
        try:
            if source.log_type == 'file':
                self._read_file_source(source)
            elif source.log_type == 'docker':
                self._read_docker_source(source)
        except Exception as e:
            print(f"Error reading source {source.name}: {e}")
    
    def _read_file_source(self, source):
        """Read from file-based log source"""
        if not os.path.exists(source.path):
            return
        
        # Check for log rotation
        source.check_rotation()
        
        try:
            with open(source.path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(source.last_position)
                
                for line in f:
                    if line.strip():
                        parsed = source.parser(line)
                        self._process_log_entry(parsed)
                
                source.last_position = f.tell()
                source.is_active = True
                
        except Exception as e:
            print(f"Error reading file {source.path}: {e}")
            source.is_active = False
    
    def _read_docker_source(self, source):
        """Read from Docker container logs"""
        try:
            # Use docker logs command with --since to get recent logs
            cmd = ['docker', 'logs', '--since', '1m', '--timestamps', source.path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if line.strip():
                        parsed = source.parser(line)
                        self._process_log_entry(parsed)
                source.is_active = True
            else:
                source.is_active = False
                
        except Exception as e:
            print(f"Error reading Docker logs for {source.path}: {e}")
            source.is_active = False
    
    def _process_log_entry(self, entry):
        """Process a parsed log entry"""
        # Add to buffer
        self.log_buffer.append(entry)
        
        # Update statistics
        self.stats['total_entries'] += 1
        self.stats[f"source_{entry['source']}"] += 1
        self.stats[f"level_{entry['level']}"] += 1
        
        # Notify subscribers
        for callback in self.subscribers:
            try:
                callback(entry)
            except:
                pass
    
    def subscribe(self, callback):
        """Subscribe to log events"""
        self.subscribers.append(callback)
    
    def get_recent_logs(self, count=100, source_filter=None, level_filter=None):
        """Get recent log entries with optional filtering"""
        logs = list(self.log_buffer)
        
        # Apply filters
        if source_filter:
            logs = [log for log in logs if log['source'] == source_filter]
        
        if level_filter:
            logs = [log for log in logs if log['level'] == level_filter]
        
        # Return most recent entries
        return logs[-count:] if count else logs
    
    def get_stats(self):
        """Get current statistics"""
        return dict(self.stats)
    
    def search_logs(self, query, count=100):
        """Search logs for a specific query"""
        results = []
        query_lower = query.lower()
        
        for log in reversed(self.log_buffer):
            if query_lower in log['message'].lower():
                results.append(log)
                if len(results) >= count:
                    break
        
        return list(reversed(results))

# Global log streamer instance
log_streamer = LogStreamer()
