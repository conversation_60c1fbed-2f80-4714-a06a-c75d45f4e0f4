"""
Admin routes for the Security Monitor Plugin
"""

from flask import Blueprint, render_template, request, jsonify, current_app, session, Response, redirect, url_for
from CTFd.utils.decorators import admins_only
from CTFd.models import db
from datetime import datetime, timedelta
from sqlalchemy import func

from .models import SecurityEvent, SecurityAlert, SecurityConfig
from .alerts import AlertManager

# Create blueprint for security monitor routes
security_monitor_bp = Blueprint(
    'security_monitor',
    __name__,
    template_folder='templates',
    static_folder='assets',
    url_prefix='/admin/plugins/security_monitor'
)


@security_monitor_bp.route('/dashboard')
@admins_only
def admin_dashboard():
    """
    Main dashboard - redirect to security dashboard
    """
    return redirect(url_for('security_monitor.security_dashboard'))


@security_monitor_bp.route('/platform')
@admins_only
def platform_dashboard():
    """
    Platform dashboard removed - redirect to security dashboard
    """
    return redirect(url_for('security_monitor.security_dashboard'))


@security_monitor_bp.route('/security')
@admins_only
def security_dashboard():
    """
    Security Monitoring Dashboard - Threats, Alerts, Events
    """
    # Get security statistics
    if hasattr(current_app, 'security_monitor'):
        stats = current_app.security_monitor.get_dashboard_stats()
    else:
        stats = {
            'recent_events': 0,
            'active_users': 0,
            'failed_logins': 0,
            'recent_alerts': [],
            'top_ips': [],
            'request_rate': 0
        }

    # Get alert summary
    alert_manager = AlertManager()
    alert_summary = alert_manager.get_alert_summary()

    # Get event type counts
    if hasattr(current_app, 'security_monitor'):
        event_counts = current_app.security_monitor.get_event_counts_by_type()
    else:
        event_counts = {}

    return render_template(
        'security_monitor/admin/security_dashboard.html',
        stats=stats,
        alert_summary=alert_summary,
        event_counts=event_counts
    )


@security_monitor_bp.route('/logs')
@admins_only
def logs_dashboard():
    """
    Real-time Logs Dashboard - Live log streaming and analysis
    """
    from .log_manager import log_streamer

    # Initialize log sources if not already done
    if not log_streamer.sources:
        setup_log_sources()

    # Get current log statistics
    stats = log_streamer.get_stats()
    sources = list(log_streamer.sources.keys())
    recent_logs = log_streamer.get_recent_logs(50)

    return render_template(
        'security_monitor/admin/logs_dashboard.html',
        stats=stats,
        sources=sources,
        recent_logs=recent_logs
    )


@security_monitor_bp.route('/events')
@admins_only
def admin_events():
    """
    View security events with filtering and pagination
    """
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    event_type = request.args.get('event_type', '')
    ip_filter = request.args.get('ip', '')
    hours = request.args.get('hours', 24, type=int)

    # Build query
    query = SecurityEvent.query

    # Apply filters
    if event_type:
        query = query.filter(SecurityEvent.event_type == event_type)

    if ip_filter:
        query = query.filter(SecurityEvent.ip_address.like(f'%{ip_filter}%'))

    # Time filter
    cutoff = datetime.utcnow() - timedelta(hours=hours)
    query = query.filter(SecurityEvent.timestamp > cutoff)

    # Order by timestamp descending
    query = query.order_by(SecurityEvent.timestamp.desc())

    # Paginate
    events = query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    # Get unique event types for filter dropdown
    event_types = db.session.query(SecurityEvent.event_type).distinct().all()
    event_types = [et[0] for et in event_types if et[0]]

    return render_template(
        'security_monitor/admin/events.html',
        events=events,
        event_types=event_types,
        current_filters={
            'event_type': event_type,
            'ip': ip_filter,
            'hours': hours
        }
    )


@security_monitor_bp.route('/alerts')
@admins_only
def admin_alerts():
    """
    View and manage security alerts
    """
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    severity = request.args.get('severity', '')
    resolved = request.args.get('resolved', 'false')

    # Build query
    query = SecurityAlert.query

    # Apply filters
    if severity:
        query = query.filter(SecurityAlert.severity == severity)

    if resolved == 'false':
        query = query.filter(SecurityAlert.resolved == False)
    elif resolved == 'true':
        query = query.filter(SecurityAlert.resolved == True)

    # Order by timestamp descending
    query = query.order_by(SecurityAlert.timestamp.desc())

    # Paginate
    alerts = query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    return render_template(
        'security_monitor/admin/alerts.html',
        alerts=alerts,
        current_filters={
            'severity': severity,
            'resolved': resolved
        }
    )


@security_monitor_bp.route('/api/stats')
@admins_only
def api_stats():
    """
    API endpoint for real-time dashboard statistics
    """
    if hasattr(current_app, 'security_monitor'):
        stats = current_app.security_monitor.get_dashboard_stats()
    else:
        stats = {
            'recent_events': 0,
            'active_users': 0,
            'failed_logins': 0,
            'recent_alerts': [],
            'top_ips': [],
            'request_rate': 0
        }

    # Convert alert objects to dictionaries for JSON serialization
    if 'recent_alerts' in stats:
        stats['recent_alerts'] = [
            {
                'id': alert.id,
                'title': alert.title,
                'severity': alert.severity,
                'timestamp': alert.timestamp.isoformat(),
                'ip_address': alert.ip_address
            }
            for alert in stats['recent_alerts']
        ]

    # Convert top IPs to list of dictionaries
    if 'top_ips' in stats:
        stats['top_ips'] = [
            {'ip': ip, 'count': count}
            for ip, count in stats['top_ips']
        ]

    return jsonify(stats)


# Platform stats API removed - Security Monitor only


@security_monitor_bp.route('/api/logs')
@admins_only
def api_logs():
    """
    API endpoint for log data (polling fallback)
    """
    from .log_manager import log_streamer

    try:
        # Get query parameters
        source_filter = request.args.get('source')
        level_filter = request.args.get('level')
        count = int(request.args.get('count', 100))
        search_query = request.args.get('search')

        if search_query:
            logs = log_streamer.search_logs(search_query, count)
        else:
            logs = log_streamer.get_recent_logs(
                count=count,
                source_filter=source_filter,
                level_filter=level_filter
            )

        stats = log_streamer.get_stats()
        sources = list(log_streamer.sources.keys())

        return jsonify({
            'logs': logs,
            'stats': stats,
            'sources': sources,
            'timestamp': datetime.utcnow().isoformat()
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'logs': [],
            'stats': {},
            'sources': []
        })


@security_monitor_bp.route('/api/log-sources')
@admins_only
def api_log_sources():
    """
    API endpoint to get available log sources
    """
    from .log_manager import log_streamer

    sources_info = {}
    for name, source in log_streamer.sources.items():
        sources_info[name] = {
            'name': source.name,
            'path': source.path,
            'type': source.log_type,
            'active': source.is_active,
            'last_position': source.last_position
        }

    return jsonify({
        'sources': sources_info,
        'total_sources': len(sources_info),
        'active_sources': sum(1 for s in log_streamer.sources.values() if s.is_active)
    })


def setup_log_sources():
    """
    Setup and initialize log sources
    """
    from .log_manager import log_streamer, LogParser
    import json

    # Try to load discovered log sources
    try:
        with open('log_sources_scan.json', 'r') as f:
            scan_results = json.load(f)

        # Add CTFd log sources
        for log_path in scan_results.get('ctfd_logs', {}):
            log_streamer.add_file_source(
                f"ctfd_{os.path.basename(log_path)}",
                log_path,
                LogParser.parse_ctfd_log
            )

        # Add system log sources
        for log_path in scan_results.get('system_logs', {}):
            if 'nginx' in log_path:
                log_streamer.add_file_source(
                    f"nginx_{os.path.basename(log_path)}",
                    log_path,
                    LogParser.parse_nginx_access
                )
            else:
                log_streamer.add_file_source(
                    f"system_{os.path.basename(log_path)}",
                    log_path
                )

        # Add Prometheus metrics source
        log_streamer.add_prometheus_source("ctfd_metrics")

    except Exception as e:
        print(f"Error setting up log sources: {e}")

    # Create some sample log files if none exist
    if not log_streamer.sources:
        create_sample_logs()

    # Start the log streaming
    log_streamer.start_streaming()


def create_sample_logs():
    """
    Create sample log files for demonstration
    """
    import os
    from datetime import datetime

    # Create logs directory
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)

    # Create sample CTFd log
    ctfd_log_path = os.path.join(logs_dir, "ctfd_app.log")
    with open(ctfd_log_path, 'w') as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S,000')} INFO [app.py:45] CTFd application started\n")
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S,001')} INFO [auth.py:123] User login attempt from *************\n")
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S,002')} WARNING [security.py:67] Rate limit exceeded for IP ************\n")

    # Create sample access log
    access_log_path = os.path.join(logs_dir, "access.log")
    with open(access_log_path, 'w') as f:
        f.write(f'************* - - [{datetime.now().strftime("%d/%b/%Y:%H:%M:%S +0000")}] "GET /login HTTP/1.1" 200 1234\n')
        f.write(f'************ - - [{datetime.now().strftime("%d/%b/%Y:%H:%M:%S +0000")}] "POST /api/v1/challenges HTTP/1.1" 429 567\n')

    # Create sample Prometheus metrics
    prometheus_log_path = os.path.join(logs_dir, "prometheus_metrics.log")
    if not os.path.exists(prometheus_log_path):
        with open(prometheus_log_path, 'w') as f:
            f.write(f"# CTFd Prometheus Metrics - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"ctfd_requests_total 1234\n")
            f.write(f"ctfd_active_users 45\n")
            f.write(f"ctfd_failed_logins_total 12\n")
            f.write(f"ctfd_response_time_seconds 0.234\n")

    # Add these sample logs to the streamer
    from .log_manager import log_streamer, LogParser
    log_streamer.add_file_source("ctfd_app", ctfd_log_path, LogParser.parse_ctfd_log)
    log_streamer.add_file_source("access_log", access_log_path, LogParser.parse_nginx_access)
    log_streamer.add_file_source("prometheus_metrics", prometheus_log_path, LogParser.parse_prometheus_metrics)


@security_monitor_bp.route('/api/metrics')
@admins_only
def api_metrics():
    """
    API endpoint for dashboard metrics (compatible with the dashboard template)
    """
    try:
        # Get platform metrics from the app
        platform_metrics = getattr(current_app, 'platform_metrics', {
            'requests_total': 0,
            'requests_by_endpoint': {},
            'active_users': set(),
            'failed_logins_total': 0,
            'response_times': [],
            'request_timestamps': []
        })

        # Calculate requests per minute from timestamps
        now = datetime.utcnow().timestamp()
        recent_timestamps = [t for t in platform_metrics.get('request_timestamps', []) if now - t < 3600]  # Last hour

        # Group by minute
        requests_per_minute = []
        if recent_timestamps:
            # Create minute buckets
            start_minute = int(min(recent_timestamps) // 60)
            end_minute = int(now // 60)

            for minute in range(start_minute, end_minute + 1):
                minute_start = minute * 60
                minute_end = (minute + 1) * 60
                count = sum(1 for t in recent_timestamps if minute_start <= t < minute_end)
                requests_per_minute.append([minute, count])

        # Get top endpoints
        top_endpoints = sorted(
            platform_metrics.get('requests_by_endpoint', {}).items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]

        # Get recent alerts from the alert log
        alert_log = getattr(current_app, 'alert_log', [])
        recent_alerts = alert_log[-10:] if alert_log else []

        # Calculate container count (placeholder - would need Docker integration)
        container_count = 0

        return jsonify({
            'total_requests': platform_metrics.get('requests_total', 0),
            'active_users': len(platform_metrics.get('active_users', set())),
            'failed_logins_total': platform_metrics.get('failed_logins_total', 0),
            'container_count': container_count,
            'requests_per_minute': requests_per_minute,
            'top_endpoints': top_endpoints,
            'alerts': recent_alerts
        })

    except Exception as e:
        return jsonify({
            'total_requests': 0,
            'active_users': 0,
            'failed_logins_total': 0,
            'container_count': 0,
            'requests_per_minute': [],
            'top_endpoints': [],
            'alerts': [],
            'error': str(e)
        })


@security_monitor_bp.route('/api/alerts/<int:alert_id>/resolve', methods=['POST'])
@admins_only
def api_resolve_alert(alert_id):
    """
    API endpoint to resolve an alert
    """
    alert_manager = AlertManager()
    user_id = session.get('id')

    if alert_manager.resolve_alert(alert_id, user_id):
        return jsonify({'success': True, 'message': 'Alert resolved successfully'})
    else:
        return jsonify({'success': False, 'message': 'Failed to resolve alert'}), 400


@security_monitor_bp.route('/api/events/chart')
@admins_only
def api_events_chart():
    """
    API endpoint for events chart data
    """
    hours = request.args.get('hours', 24, type=int)
    cutoff = datetime.utcnow() - timedelta(hours=hours)

    # Get events grouped by hour and type
    events_by_hour = db.session.query(
        func.date_trunc('hour', SecurityEvent.timestamp).label('hour'),
        SecurityEvent.event_type,
        func.count(SecurityEvent.id).label('count')
    ).filter(
        SecurityEvent.timestamp > cutoff
    ).group_by(
        'hour',
        SecurityEvent.event_type
    ).order_by('hour').all()

    # Format data for chart
    chart_data = {}
    for hour, event_type, count in events_by_hour:
        if hour:
            hour_str = hour.strftime('%Y-%m-%d %H:00')
            if hour_str not in chart_data:
                chart_data[hour_str] = {}
            chart_data[hour_str][event_type] = count

    return jsonify(chart_data)


@security_monitor_bp.route('/api/top-threats')
@admins_only
def api_top_threats():
    """
    API endpoint for top threat IPs
    """
    hours = request.args.get('hours', 24, type=int)
    cutoff = datetime.utcnow() - timedelta(hours=hours)

    # Get IPs with most security events
    threat_ips = db.session.query(
        SecurityEvent.ip_address,
        func.count(SecurityEvent.id).label('total_events'),
        func.sum(
            db.case(
                [(SecurityEvent.event_type == 'failed_login', 1)],
                else_=0
            )
        ).label('failed_logins'),
        func.sum(
            db.case(
                [(SecurityEvent.event_type == 'rate_limit_exceeded', 1)],
                else_=0
            )
        ).label('rate_violations')
    ).filter(
        SecurityEvent.timestamp > cutoff
    ).group_by(
        SecurityEvent.ip_address
    ).order_by(
        func.count(SecurityEvent.id).desc()
    ).limit(10).all()

    # Format data
    threats = [
        {
            'ip': ip,
            'total_events': total,
            'failed_logins': failed or 0,
            'rate_violations': violations or 0
        }
        for ip, total, failed, violations in threat_ips
    ]

    return jsonify(threats)


@security_monitor_bp.route('/config')
@admins_only
def admin_config():
    """
    Security monitor configuration page
    """
    # Get current configuration
    configs = SecurityConfig.query.all()
    config_dict = {c.key: c.value for c in configs}

    # Default configurations
    default_config = {
        'rate_limit_threshold': '60',
        'failed_login_threshold': '10',
        'alert_email': '',
        'auto_block_enabled': 'false',
        'auto_block_duration': '3600'
    }

    # Merge with defaults
    for key, value in default_config.items():
        if key not in config_dict:
            config_dict[key] = value

    return render_template(
        'security_monitor/admin/config.html',
        config=config_dict
    )


@security_monitor_bp.route('/api/config', methods=['POST'])
@admins_only
def api_save_config():
    """
    Save security monitor configuration
    """
    try:
        data = request.get_json()

        for key, value in data.items():
            config = SecurityConfig.query.filter_by(key=key).first()
            if config:
                config.value = str(value)
            else:
                config = SecurityConfig(key=key, value=str(value))
                db.session.add(config)

        db.session.commit()
        return jsonify({'success': True, 'message': 'Configuration saved successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 400


@security_monitor_bp.route('/api/prometheus')
def prometheus_metrics():
    """
    Export metrics in Prometheus format
    """
    # Public endpoint for Prometheus scraping - no admin check
    output = []

    try:
        # Get platform metrics from the app
        platform_metrics = getattr(current_app, 'platform_metrics', {
            'requests_total': 0,
            'requests_by_endpoint': {},
            'active_users': set(),
            'failed_logins_total': 0,
            'response_times': [],
            'request_timestamps': []
        })

        # PLATFORM METRICS (for CTFd Platform Metrics Dashboard)
        output.append('# Platform Metrics')
        output.append('')

        # Total requests
        output.append('# HELP ctfd_requests_total Total number of requests')
        output.append('# TYPE ctfd_requests_total counter')
        output.append(f'ctfd_requests_total {platform_metrics.get("requests_total", 0)}')

        # Requests by endpoint
        output.append('# HELP ctfd_requests_by_endpoint Requests by endpoint')
        output.append('# TYPE ctfd_requests_by_endpoint counter')
        for endpoint, count in platform_metrics.get('requests_by_endpoint', {}).items():
            if endpoint:
                safe_endpoint = endpoint.replace('.', '_').replace('/', '_')
                output.append(f'ctfd_requests_by_endpoint{{endpoint="{safe_endpoint}"}} {count}')

        # Active users
        output.append('# HELP ctfd_active_users Number of unique IPs seen')
        output.append('# TYPE ctfd_active_users gauge')
        active_users = platform_metrics.get("active_users", set())
        output.append(f'ctfd_active_users {len(active_users)}')

        # Failed logins
        output.append('# HELP ctfd_failed_logins Total failed login attempts')
        output.append('# TYPE ctfd_failed_logins counter')
        output.append(f'ctfd_failed_logins {platform_metrics.get("failed_logins_total", 0)}')

        # Response time
        response_times = platform_metrics.get('response_times', [])
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            output.append('# HELP ctfd_response_time_seconds Average response time')
            output.append('# TYPE ctfd_response_time_seconds gauge')
            output.append(f'ctfd_response_time_seconds {avg_response_time:.4f}')

        # Add CTFd-specific platform metrics
        from CTFd.models import Challenges, Submissions

        # Total challenges
        total_challenges = Challenges.query.count()
        output.append('# HELP ctfd_challenges_total Total number of challenges')
        output.append('# TYPE ctfd_challenges_total gauge')
        output.append(f'ctfd_challenges_total {total_challenges}')

        # Total submissions
        total_submissions = Submissions.query.count()
        output.append('# HELP ctfd_submissions_total Total challenge submissions')
        output.append('# TYPE ctfd_submissions_total counter')
        output.append(f'ctfd_submissions_total {total_submissions}')

        output.append('')
        output.append('# Security Monitor Metrics')
        output.append('')

        # Security events metrics
        now = datetime.utcnow()
        hour_ago = now - timedelta(hours=1)
        day_ago = now - timedelta(days=1)

        # Total events
        total_events = SecurityEvent.query.count()
        output.append('# HELP ctfd_security_events_total Total number of security events')
        output.append('# TYPE ctfd_security_events_total counter')
        output.append(f'ctfd_security_events_total {total_events}')

        # Events by type
        event_counts = db.session.query(
            SecurityEvent.event_type,
            func.count(SecurityEvent.id).label('count')
        ).group_by(SecurityEvent.event_type).all()

        output.append('# HELP ctfd_security_events_by_type Security events by type')
        output.append('# TYPE ctfd_security_events_by_type counter')
        for event_type, count in event_counts:
            if event_type:
                output.append(f'ctfd_security_events_by_type{{type="{event_type}"}} {count}')

        # Recent events (last hour)
        recent_events = SecurityEvent.query.filter(
            SecurityEvent.timestamp > hour_ago
        ).count()
        output.append('# HELP ctfd_security_events_recent Events in the last hour')
        output.append('# TYPE ctfd_security_events_recent gauge')
        output.append(f'ctfd_security_events_recent {recent_events}')

        # Failed logins
        failed_logins = SecurityEvent.query.filter_by(
            event_type='failed_login'
        ).filter(
            SecurityEvent.timestamp > day_ago
        ).count()
        output.append('# HELP ctfd_failed_logins_24h Failed logins in last 24 hours')
        output.append('# TYPE ctfd_failed_logins_24h gauge')
        output.append(f'ctfd_failed_logins_24h {failed_logins}')

        # Rate limit violations
        rate_violations = SecurityEvent.query.filter_by(
            event_type='rate_limit_exceeded'
        ).filter(
            SecurityEvent.timestamp > hour_ago
        ).count()
        output.append('# HELP ctfd_rate_limit_violations_1h Rate limit violations in last hour')
        output.append('# TYPE ctfd_rate_limit_violations_1h gauge')
        output.append(f'ctfd_rate_limit_violations_1h {rate_violations}')

        # Active alerts
        active_alerts = SecurityAlert.query.filter_by(resolved=False).count()
        output.append('# HELP ctfd_security_alerts_active Number of active security alerts')
        output.append('# TYPE ctfd_security_alerts_active gauge')
        output.append(f'ctfd_security_alerts_active {active_alerts}')

        # Alerts by severity
        alert_severities = db.session.query(
            SecurityAlert.severity,
            func.count(SecurityAlert.id).label('count')
        ).filter_by(
            resolved=False
        ).group_by(SecurityAlert.severity).all()

        output.append('# HELP ctfd_security_alerts_by_severity Active alerts by severity')
        output.append('# TYPE ctfd_security_alerts_by_severity gauge')
        for severity, count in alert_severities:
            if severity:
                output.append(f'ctfd_security_alerts_by_severity{{severity="{severity}"}} {count}')

        # Top IPs by events
        top_ips = db.session.query(
            SecurityEvent.ip_address,
            func.count(SecurityEvent.id).label('count')
        ).filter(
            SecurityEvent.timestamp > hour_ago
        ).group_by(
            SecurityEvent.ip_address
        ).order_by(
            func.count(SecurityEvent.id).desc()
        ).limit(10).all()

        output.append('# HELP ctfd_security_events_by_ip Top IPs by security events')
        output.append('# TYPE ctfd_security_events_by_ip gauge')
        for ip, count in top_ips:
            if ip:
                # Sanitize IP for Prometheus label
                safe_ip = ip.replace('.', '_').replace(':', '_')
                output.append(f'ctfd_security_events_by_ip{{ip="{safe_ip}"}} {count}')

    except Exception as e:
        # Log error but continue
        output.append(f'# Error generating metrics: {str(e)}')

    return Response('\n'.join(output), mimetype='text/plain')
