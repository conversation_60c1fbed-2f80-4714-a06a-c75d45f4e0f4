[{"timestamp": "2025-05-29T20:10:30.724508", "event_type": "failed_login", "ip": "**********", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T09:07:00.396702", "event_type": "rate_limit_exceeded", "ip": "*************", "details": {"username": "user1", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T14:02:52.141679", "event_type": "failed_login", "ip": "***********", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T21:44:32.590445", "event_type": "rate_limit_exceeded", "ip": "*************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T05:34:00.162455", "event_type": "successful_login", "ip": "*************", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T17:52:18.859115", "event_type": "successful_login", "ip": "**********", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T18:15:13.325259", "event_type": "failed_login", "ip": "*************", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T03:13:30.780382", "event_type": "challenge_submission", "ip": "*************", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T11:58:35.268210", "event_type": "challenge_submission", "ip": "************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T00:02:50.955334", "event_type": "challenge_submission", "ip": "**********", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T16:59:04.713170", "event_type": "failed_login", "ip": "************", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T14:36:15.970371", "event_type": "successful_login", "ip": "*************", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T03:45:36.625871", "event_type": "rate_limit_exceeded", "ip": "*************", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T02:17:58.294181", "event_type": "failed_login", "ip": "127.0.0.1", "details": {"username": "guest", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T07:44:51.997171", "event_type": "user_registration", "ip": "*************", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T20:43:46.761890", "event_type": "successful_login", "ip": "*************", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T08:46:51.973422", "event_type": "admin_action", "ip": "*************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T16:55:54.222509", "event_type": "user_registration", "ip": "127.0.0.1", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T05:29:50.405196", "event_type": "challenge_submission", "ip": "**********", "details": {"username": "guest", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T21:50:06.700682", "event_type": "successful_login", "ip": "*************", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T12:17:22.422499", "event_type": "user_registration", "ip": "*************", "details": {"username": "guest", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T13:28:54.007840", "event_type": "user_registration", "ip": "*********", "details": {"username": "guest", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T13:00:39.917979", "event_type": "challenge_submission", "ip": "*************", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T09:24:22.006712", "event_type": "failed_login", "ip": "127.0.0.1", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T21:44:29.259065", "event_type": "rate_limit_exceeded", "ip": "**********", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T12:27:43.890379", "event_type": "challenge_submission", "ip": "*************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T06:20:23.939746", "event_type": "user_registration", "ip": "127.0.0.1", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T05:35:04.535654", "event_type": "rate_limit_exceeded", "ip": "*************", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T14:59:59.039324", "event_type": "user_registration", "ip": "*************", "details": {"username": "guest", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T10:47:13.650768", "event_type": "rate_limit_exceeded", "ip": "*************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T13:39:32.176642", "event_type": "failed_login", "ip": "*********", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T09:10:58.793340", "event_type": "failed_login", "ip": "**********", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T06:57:15.950194", "event_type": "challenge_submission", "ip": "*************", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T11:13:32.421658", "event_type": "successful_login", "ip": "*************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T17:10:06.688248", "event_type": "admin_action", "ip": "*************", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T09:09:02.097792", "event_type": "successful_login", "ip": "*********", "details": {"username": "guest", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T03:58:37.930414", "event_type": "user_registration", "ip": "*************", "details": {"username": "guest", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T03:23:49.773737", "event_type": "rate_limit_exceeded", "ip": "*************", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T09:34:44.662475", "event_type": "failed_login", "ip": "*************", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T07:58:54.097541", "event_type": "admin_action", "ip": "*************", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-28T23:39:02.377272", "event_type": "successful_login", "ip": "*************", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T13:03:03.156320", "event_type": "challenge_submission", "ip": "**********", "details": {"username": "user1", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T12:09:00.278004", "event_type": "rate_limit_exceeded", "ip": "***********", "details": {"username": "guest", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T16:41:24.728023", "event_type": "admin_action", "ip": "127.0.0.1", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T17:43:11.022404", "event_type": "successful_login", "ip": "127.0.0.1", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T01:19:54.765959", "event_type": "challenge_submission", "ip": "************", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T23:20:48.218695", "event_type": "user_registration", "ip": "*************", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T20:34:57.665300", "event_type": "rate_limit_exceeded", "ip": "*************", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T07:05:47.781521", "event_type": "successful_login", "ip": "**********", "details": {"username": "user1", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T04:06:07.410307", "event_type": "rate_limit_exceeded", "ip": "***********", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T15:38:41.376954", "event_type": "failed_login", "ip": "************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T22:03:50.313233", "event_type": "failed_login", "ip": "**********", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T03:19:20.999154", "event_type": "user_registration", "ip": "*************", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T08:34:00.333171", "event_type": "failed_login", "ip": "*************", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T10:49:57.091904", "event_type": "failed_login", "ip": "*************", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T19:24:55.677773", "event_type": "failed_login", "ip": "*********", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-28T23:57:44.422259", "event_type": "rate_limit_exceeded", "ip": "**********", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T08:49:32.049539", "event_type": "challenge_submission", "ip": "*********", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T00:36:10.657851", "event_type": "user_registration", "ip": "***********", "details": {"username": "user1", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T16:21:21.826976", "event_type": "successful_login", "ip": "*************", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T13:58:18.042659", "event_type": "failed_login", "ip": "*********", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T02:56:59.220731", "event_type": "user_registration", "ip": "*************", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T05:44:41.277077", "event_type": "challenge_submission", "ip": "*************", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T00:09:33.832212", "event_type": "failed_login", "ip": "*************", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T20:31:11.812088", "event_type": "rate_limit_exceeded", "ip": "*********", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T06:08:59.699791", "event_type": "admin_action", "ip": "***********", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T21:51:43.382542", "event_type": "rate_limit_exceeded", "ip": "127.0.0.1", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T22:14:41.229225", "event_type": "failed_login", "ip": "**********", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T11:21:25.302282", "event_type": "admin_action", "ip": "**********", "details": {"username": "user1", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T07:37:50.285188", "event_type": "challenge_submission", "ip": "*************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T14:27:20.416081", "event_type": "admin_action", "ip": "***********", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T17:11:11.244451", "event_type": "failed_login", "ip": "************", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T04:45:31.044659", "event_type": "rate_limit_exceeded", "ip": "*************", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T21:09:05.333650", "event_type": "rate_limit_exceeded", "ip": "**********", "details": {"username": "user1", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T17:29:23.115199", "event_type": "successful_login", "ip": "*********", "details": {"username": "user1", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T21:55:06.812890", "event_type": "rate_limit_exceeded", "ip": "*************", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T18:08:27.041620", "event_type": "user_registration", "ip": "*************", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T02:25:06.191984", "event_type": "admin_action", "ip": "*************", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T13:35:10.958081", "event_type": "failed_login", "ip": "**********", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T06:40:31.606764", "event_type": "admin_action", "ip": "*************", "details": {"username": "user1", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T16:04:05.898174", "event_type": "admin_action", "ip": "************", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T04:44:37.576053", "event_type": "failed_login", "ip": "************", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T09:41:57.466236", "event_type": "user_registration", "ip": "************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T14:33:10.603253", "event_type": "failed_login", "ip": "*************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T13:54:20.687062", "event_type": "failed_login", "ip": "*************", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T16:05:40.870090", "event_type": "rate_limit_exceeded", "ip": "127.0.0.1", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T05:27:09.582647", "event_type": "challenge_submission", "ip": "**********", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T01:15:33.701961", "event_type": "admin_action", "ip": "**********", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T05:58:40.692803", "event_type": "rate_limit_exceeded", "ip": "127.0.0.1", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T08:00:35.820205", "event_type": "rate_limit_exceeded", "ip": "*************", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T03:27:21.972134", "event_type": "admin_action", "ip": "*************", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T20:58:48.953150", "event_type": "user_registration", "ip": "*************", "details": {"username": "hacker", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T20:36:31.559582", "event_type": "failed_login", "ip": "**********", "details": {"username": "demo", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T01:12:20.494933", "event_type": "user_registration", "ip": "*************", "details": {"username": "admin", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T05:44:33.708275", "event_type": "challenge_submission", "ip": "**********", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T01:33:36.466437", "event_type": "failed_login", "ip": "***********", "details": {"username": "administrator", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}, {"timestamp": "2025-05-29T01:55:48.467486", "event_type": "user_registration", "ip": "*********", "details": {"username": "user1", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/challenges"}}, {"timestamp": "2025-05-29T10:17:58.833675", "event_type": "successful_login", "ip": "*************", "details": {"username": "user1", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/login"}}, {"timestamp": "2025-05-29T20:30:53.864403", "event_type": "failed_login", "ip": "***********", "details": {"username": "root", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/admin"}}, {"timestamp": "2025-05-29T00:04:58.352317", "event_type": "successful_login", "ip": "*********", "details": {"username": "test", "user_agent": "Mozilla/5.0 (<PERSON> Browser)", "endpoint": "/register"}}]